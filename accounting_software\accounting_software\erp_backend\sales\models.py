from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from decimal import Decimal
import uuid
from django.utils import timezone


class PaymentTerm(models.Model):
    """Payment terms that can be used across customers, invoices, etc."""
    
    name = models.CharField(max_length=100, unique=True, help_text="E.g., 'Net 30', 'Due on Receipt'")
    code = models.CharField(max_length=50, unique=True, help_text="E.g., 'net_30', 'due_on_receipt'")
    days = models.PositiveIntegerField(help_text="Number of days from invoice date")
    description = models.TextField(blank=True, null=True, help_text="Optional description")
    is_default = models.BooleanField(default=False, help_text="Is this the default payment term?")
    is_active = models.BooleanField(default=True, help_text="Is this payment term active?")
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='payment_terms_created')
    
    class Meta:
        db_table = 'sales_payment_terms'
        ordering = ['days', 'name']
        verbose_name = 'Payment Term'
        verbose_name_plural = 'Payment Terms'
    
    def __str__(self):
        return f"{self.name} ({self.days} days)"
    
    def save(self, *args, **kwargs):
        # Ensure only one default payment term
        if self.is_default:
            PaymentTerm.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)


# Customer model is now in contacts app - using contacts.Customer instead


class ProductCategory(models.Model):
    """Enhanced Product categories for retail inventory management"""
    
    DIVISION_TYPE_CHOICES = [
        ('perishable', 'Perishable'),
        ('refrigerated', 'Refrigerated'),
        ('frozen', 'Frozen'),
        ('controlled-substance', 'Controlled Substance'),
        ('non-perishable', 'Non-Perishable'),
    ]
    
    UNIT_OF_MEASURE_CHOICES = [
        ('piece', 'Piece'),
        ('kg', 'Kilogram'),
        ('gram', 'Gram'),
        ('liter', 'Liter'),
        ('ml', 'Milliliter'),
        ('box', 'Box'),
        ('pack', 'Pack'),
        ('dozen', 'Dozen'),
        ('meter', 'Meter'),
        ('cm', 'Centimeter'),
    ]
    
    # Basic Information
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(max_length=20, unique=True, default='CAT', help_text="Short code for the category (e.g., FRT, VEG)")
    description = models.TextField(blank=True, null=True)
    
    # Hierarchy
    parent_category = models.ForeignKey('self', on_delete=models.CASCADE, blank=True, null=True, related_name='subcategories')
    level = models.PositiveIntegerField(default=1, help_text="Category hierarchy level")
    
    # Division Type
    division_type = models.CharField(max_length=30, choices=DIVISION_TYPE_CHOICES, default='non-perishable')
    
    # Visual
    image_url = models.URLField(blank=True, null=True, help_text="Category image URL")
    
    # Business Settings
    tax_category = models.CharField(max_length=50, blank=True, null=True, help_text="Default tax category for products")
    margin_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0.00, help_text="Default profit margin %")
    
    # Settings
    is_active = models.BooleanField(default=True)
    sort_order = models.PositiveIntegerField(default=1, help_text="Display order")
    allow_subcategories = models.BooleanField(default=True)
    requires_expiry_tracking = models.BooleanField(default=False)
    requires_batch_tracking = models.BooleanField(default=False)
    default_unit_of_measure = models.CharField(max_length=20, choices=UNIT_OF_MEASURE_CHOICES, default='piece')
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='categories_created')
    
    class Meta:
        db_table = 'sales_product_categories'
        verbose_name_plural = 'Product Categories'
        ordering = ['sort_order', 'name']
        unique_together = [['parent_category', 'name']]  # Allow same name in different parent categories
    
    def __str__(self):
        return f"{self.code} - {self.name}"
    
    def save(self, *args, **kwargs):
        # Calculate level based on parent
        if self.parent_category:
            self.level = self.parent_category.level + 1
        else:
            self.level = 1
        
        # Validate level doesn't exceed 3
        if self.level > 3:
            raise ValueError("Category hierarchy cannot exceed 3 levels")
        
        super().save(*args, **kwargs)
    
    @property
    def full_path(self):
        """Get full category path (e.g., 'Food > Fruits > Citrus')"""
        if self.parent_category:
            return f"{self.parent_category.full_path} > {self.name}"
        return self.name
    
    @property
    def products_count(self):
        """Count of products in this category"""
        return self.products.filter(status='active').count()
    
    @property
    def subcategories_count(self):
        """Count of subcategories"""
        return self.subcategories.filter(is_active=True).count()
    
    def get_all_subcategories(self):
        """Get all subcategories recursively"""
        subcategories = list(self.subcategories.filter(is_active=True))
        for subcategory in self.subcategories.filter(is_active=True):
            subcategories.extend(subcategory.get_all_subcategories())
        return subcategories


class Product(models.Model):
    """Enhanced Product and Service model with Sales Price Authority"""
    
    PRODUCT_TYPE_CHOICES = [
        ('product', 'Product'),
        ('service', 'Service'),
        ('bundle', 'Bundle'),
    ]
    
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
    ]
    
    # Basic Information
    product_id = models.UUIDField(default=uuid.uuid4, unique=True)
    name = models.CharField(max_length=200)
    sku = models.CharField(max_length=100, unique=True, blank=True, null=True)
    product_type = models.CharField(max_length=20, choices=PRODUCT_TYPE_CHOICES, default='product')
    category = models.ForeignKey(ProductCategory, on_delete=models.SET_NULL, null=True, blank=True, related_name='products')
    description = models.TextField(blank=True, null=True)
    
    # Pricing - Enhanced with Sales Price Authority
    unit_price = models.DecimalField(
        max_digits=15, 
        decimal_places=2, 
        default=0.00,
        help_text="Sales price (managed by Sales Department)"
    )
    cost_price = models.DecimalField(
        max_digits=15, 
        decimal_places=2, 
        default=0.00, 
        blank=True, 
        null=True,
        help_text="Purchase cost (managed by Purchase Department)"
    )
    minimum_selling_price = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0.00,
        help_text="Minimum allowed selling price (for validation)"
    )
    
    # Price History and Authority
    price_effective_date = models.DateField(
        blank=True, 
        null=True,
        help_text="Date when current price became effective"
    )
    price_last_updated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='products_priced',
        help_text="User who last updated the sales price"
    )
    price_last_updated_at = models.DateTimeField(
        blank=True,
        null=True,
        help_text="When the price was last updated"
    )
    
    # GL Account Integration - PROPER FOREIGN KEY FIELDS
    income_account_gl = models.ForeignKey(
        'gl.Account',
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name='sales_products',
        limit_choices_to={'account_type__type': 'REVENUE'},
        help_text="Revenue account for sales of this product/service"
    )
    expense_account_gl = models.ForeignKey(
        'gl.Account', 
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name='expense_products',
        limit_choices_to={'account_type__type': 'EXPENSE'},
        help_text="COGS/Expense account for purchasing this product"
    )
    inventory_asset_account_gl = models.ForeignKey(
        'gl.Account',
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name='inventory_products',
        limit_choices_to={'account_type__type': 'ASSET', 'detail_type__code': 'INVENTORY'},
        help_text="Asset account for inventory tracking"
    )
    sales_tax_category = models.CharField(max_length=100, blank=True, null=True)
    
    # Purchasing Information (for products)
    preferred_vendor = models.CharField(max_length=200, blank=True, null=True)
    
    # Inventory (for products only)
    track_inventory = models.BooleanField(default=False)
    reorder_point = models.IntegerField(default=0)
    quantity_on_hand = models.IntegerField(default=0)
    quantity_on_purchase_order = models.IntegerField(default=0)
    quantity_on_sales_order = models.IntegerField(default=0)
    
    # Settings
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='active')
    taxable = models.BooleanField(default=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='products_created')
    
    class Meta:
        db_table = 'sales_products'
        ordering = ['name']
    
    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        # Track price updates
        if self.pk:  # Existing product
            old_product = Product.objects.get(pk=self.pk)
            if old_product.unit_price != self.unit_price:
                self.price_last_updated_at = timezone.now()
                # price_last_updated_by should be set by the view
        super().save(*args, **kwargs)
    
    @property
    def margin_amount(self):
        """Calculate margin amount (sales price - cost price)"""
        if self.cost_price and self.unit_price:
            return self.unit_price - self.cost_price
        return Decimal('0.00')
    
    @property
    def margin_percentage(self):
        """Calculate margin percentage"""
        if self.cost_price and self.cost_price > 0 and self.unit_price:
            return ((self.unit_price - self.cost_price) / self.cost_price) * 100
        return Decimal('0.00')
    
    @property
    def markup_percentage(self):
        """Calculate markup percentage (margin / cost price)"""
        if self.cost_price and self.cost_price > 0:
            return (self.margin_amount / self.cost_price) * 100
        return Decimal('0.00')
    
    def get_current_average_cost(self):
        """Get current weighted average cost from inventory"""
        try:
            from inventory.models import Inventory
            inventory_items = Inventory.objects.filter(product=self)
            total_qty = sum(item.quantity_on_hand for item in inventory_items)
            total_value = sum(item.quantity_on_hand * item.average_cost for item in inventory_items)
            
            if total_qty > 0:
                return total_value / total_qty
            return self.cost_price or Decimal('0.00')
        except:
            return self.cost_price or Decimal('0.00')
    
    def get_sales_account(self):
        """Get the GL account for sales revenue"""
        return self.income_account_gl
    
    def get_cogs_account(self):
        """Get the GL account for cost of goods sold"""
        return self.expense_account_gl
    
    def get_inventory_account(self):
        """Get the GL account for inventory asset"""
        return self.inventory_asset_account_gl
    
    def create_sale_journal_entry(self, quantity, sale_price, customer=None, warehouse=None):
        """Create GL journal entry for a sale of this product with proper COGS"""
        if not self.income_account_gl:
            raise ValueError(f"No income account configured for product {self.name}")
        
        from decimal import Decimal
        from django.utils import timezone
        
        total_revenue = Decimal(str(quantity)) * Decimal(str(sale_price))
        
        # Get COGS from weighted average cost
        cogs_per_unit = self.get_current_average_cost()
        total_cogs = Decimal(str(quantity)) * cogs_per_unit
        
        # Prepare journal entry data
        journal_entry_data = {
            'entry_number': f"SALE-{self.sku}-{timezone.now().strftime('%Y%m%d%H%M%S')}",
            'description': f"Sale of {self.name}",
            'transaction_date': timezone.now().date(),
            'source_document_type': 'SALE',
            'source_document_id': f"SALE-{self.id}",
            'net_amount': total_revenue,
            'lines': []
        }
        
        # Revenue entry: Dr. Accounts Receivable, Cr. Sales Revenue
        journal_entry_data['lines'].extend([
            {
                'account': 'accounts_receivable',  # Will be resolved to proper account
                'description': f"Sale to {customer.display_name if customer else 'Customer'} - {self.name}",
                'debit_amount': total_revenue,
                'credit_amount': Decimal('0.00'),
                'customer': customer.id if customer else None,
                'product': self.id,
                    'quantity': quantity,
                    'unit_price': sale_price
                },
                {
                'account': self.income_account_gl.id,
                'description': f"Sales Revenue - {self.name}",
                'debit_amount': Decimal('0.00'),
                'credit_amount': total_revenue,
                'customer': customer.id if customer else None,
                'product': self.id,
                    'quantity': quantity,
                    'unit_price': sale_price
                }
        ])
        
        # COGS entry (if inventory product): Dr. COGS, Cr. Inventory Asset
        if self.track_inventory and self.expense_account_gl and self.inventory_asset_account_gl:
            journal_entry_data['lines'].extend([
                {
                    'account': self.expense_account_gl.id,
                    'description': f"COGS - {self.name}",
                    'debit_amount': total_cogs,
                    'credit_amount': Decimal('0.00'),
                    'product': self.id,
                    'quantity': quantity,
                    'unit_price': cogs_per_unit
                },
                {
                    'account': self.inventory_asset_account_gl.id,
                    'description': f"Inventory reduction - {self.name}",
                    'debit_amount': Decimal('0.00'),
                    'credit_amount': total_cogs,
                    'product': self.id,
                    'quantity': quantity,
                    'unit_price': cogs_per_unit
                }
            ])
        
        return journal_entry_data


class SalesOrder(models.Model):
    """Sales Order model following QuickBooks structure"""

    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('acknowledged', 'Acknowledged'),
        ('partial', 'Partially Delivered'),
        ('delivered', 'Delivered'),
        ('closed', 'Closed'),
        ('cancelled', 'Cancelled'),
    ]

    # Basic Information
    so_id = models.UUIDField(default=uuid.uuid4, unique=True)
    so_number = models.CharField(max_length=50, unique=True)
    customer = models.ForeignKey('contacts.Contact', on_delete=models.SET_NULL, null=True, blank=True, related_name='sales_orders', help_text='Customer from contacts system')

    # Dates
    so_date = models.DateField()
    expected_date = models.DateField(blank=True, null=True)

    # Seller Information
    seller_name = models.CharField(max_length=200, blank=True, null=True, help_text="Name of the seller/salesperson")
    seller_email = models.EmailField(blank=True, null=True, help_text="Email of the seller/salesperson")
    seller_phone = models.CharField(max_length=20, blank=True, null=True, help_text="Phone of the seller/salesperson")

    # Financial Information
    subtotal = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    discount_percent = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    discount_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    amount_delivered = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    balance_due = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)

    # Settings
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    payment_terms = models.CharField(max_length=20, blank=True, null=True)

    # Additional Information
    reference_number = models.CharField(max_length=100, blank=True, null=True)
    memo = models.TextField(blank=True, null=True, help_text="Internal memo")
    notes = models.TextField(blank=True, null=True, help_text="Notes to customer")

    # Shipping Information
    ship_to_address = models.TextField(blank=True, null=True)

    # Email Tracking
    email_sent = models.BooleanField(default=False)
    email_sent_date = models.DateTimeField(blank=True, null=True)
    acknowledged_date = models.DateTimeField(blank=True, null=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='sales_orders_created')

    class Meta:
        db_table = 'sales_orders'
        ordering = ['-so_date', '-created_at']

    def __str__(self):
        customer_name = self.customer.name if self.customer else "No Customer"
        return f"SO {self.so_number} - {customer_name}"

    def calculate_totals(self):
        """Calculate totals from line items"""
        from decimal import Decimal

        line_items = self.line_items.all()

        # Calculate subtotal from line items
        self.subtotal = sum(item.line_total for item in line_items) or Decimal('0.00')

        # Calculate discount amount
        self.discount_amount = self.subtotal * (Decimal(str(self.discount_percent)) / 100)

        # Calculate tax amount from line items
        self.tax_amount = sum(item.tax_amount for item in line_items) or Decimal('0.00')

        # Calculate total amount
        self.total_amount = self.subtotal - self.discount_amount + self.tax_amount

        # Calculate balance due
        self.balance_due = self.total_amount - Decimal(str(self.amount_delivered))

    def save(self, *args, **kwargs):
        from decimal import Decimal

        # Auto-generate SO number if not provided
        if not self.so_number:
            last_so = SalesOrder.objects.filter(
                so_number__startswith='SO-'
            ).order_by('-created_at').first()

            if last_so:
                try:
                    last_number = int(last_so.so_number.split('-')[1])
                    new_number = last_number + 1
                except (ValueError, IndexError):
                    new_number = 1
            else:
                new_number = 1

            self.so_number = f'SO-{new_number:06d}'

        # Ensure all decimal fields are Decimal type
        self.subtotal = Decimal(str(self.subtotal))
        self.discount_percent = Decimal(str(self.discount_percent))
        self.discount_amount = Decimal(str(self.discount_amount))
        self.tax_amount = Decimal(str(self.tax_amount))
        self.total_amount = Decimal(str(self.total_amount))
        self.amount_delivered = Decimal(str(self.amount_delivered))

        # Calculate balance due
        self.balance_due = self.total_amount - self.amount_delivered

        # Only auto-set status for new SOs
        if self.pk is None:  # New SO
            if self.status not in ['draft', 'pending', 'sent', 'acknowledged', 'partial', 'delivered']:
                self.status = 'draft'

        super().save(*args, **kwargs)

    def get_product_price(self, product, customer=None, quantity=1):
        """Get product price - simplified to avoid circular import with Pricing module"""
        from decimal import Decimal

        try:
            # Use product's cost_price as the base price
            if product and hasattr(product, 'cost_price') and product.cost_price:
                return product.cost_price

            # Fallback to unit_price if available
            if product and hasattr(product, 'unit_price') and product.unit_price:
                return product.unit_price

            return Decimal('0.00')

        except Exception as e:
            print(f"Error getting product price: {e}")
            return Decimal('0.00')

    def check_inventory_availability(self, product, quantity, branch_warehouse=None):
        """Check inventory availability for product"""
        try:
            from inventory.models import Inventory
            from decimal import Decimal

            if not product:
                return False, "Product not specified"

            # Get inventory for the product
            inventory_query = Inventory.objects.filter(product=product)

            # Filter by branch warehouse if specified
            if branch_warehouse:
                inventory_query = inventory_query.filter(warehouse=branch_warehouse)

            # Sum available quantity across all matching inventory records
            total_available = inventory_query.aggregate(
                total=models.Sum('quantity_on_hand')
            )['total'] or Decimal('0.00')

            if total_available >= quantity:
                return True, f"Available: {total_available}"
            else:
                return False, f"Insufficient stock. Available: {total_available}, Required: {quantity}"

        except Exception as e:
            print(f"Error checking inventory availability: {e}")
            return False, f"Error checking availability: {e}"

    def calculate_totals(self):
        """Calculate sales order totals from line items"""
        from decimal import Decimal

        line_items = self.line_items.all()

        # Calculate subtotal
        self.subtotal = sum(
            item.line_total for item in line_items
        )

        # Apply discount
        if self.discount_percent > 0:
            self.discount_amount = (self.subtotal * self.discount_percent) / Decimal('100')
        else:
            self.discount_amount = Decimal('0.00')

        # Calculate tax amount
        self.tax_amount = sum(
            item.tax_amount for item in line_items
        )

        # Calculate total amount
        self.total_amount = self.subtotal - self.discount_amount + self.tax_amount

        # Calculate balance due (total - amount delivered)
        self.balance_due = self.total_amount - self.amount_delivered

        # Don't save here to avoid recursion - let the caller save


class SalesOrderLineItem(models.Model):
    """Sales Order line items for products and services"""

    sales_order = models.ForeignKey(SalesOrder, on_delete=models.CASCADE, related_name='line_items')

    # Product/Service Link
    product = models.ForeignKey(
        'Pricing.Product',
        on_delete=models.CASCADE,
        related_name='sales_line_items',
        null=True,
        blank=True,
        help_text="Link to product from pricing module"
    )

    # Item details
    description = models.TextField(help_text="Auto-filled from product, can be overridden")
    quantity = models.DecimalField(max_digits=10, decimal_places=2, default=1.00)
    unit_of_measure = models.CharField(max_length=20, default='pcs', help_text="Unit of measure (kg, L, pcs, etc.)")
    unit_price = models.DecimalField(max_digits=15, decimal_places=2, default=0.00, help_text="Sales price per unit")
    discount_percent = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    line_total = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)

    # Tax information
    taxable = models.BooleanField(default=True)
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)

    # Delivery information
    quantity_delivered = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    quantity_pending = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)

    # Metadata
    line_order = models.PositiveIntegerField(default=1, help_text="Order of line item in the sales order")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'sales_order_line_items'
        ordering = ['line_order', 'created_at']

    def __str__(self):
        product_name = self.product.name if self.product else "Custom Item"
        return f"{self.sales_order.so_number} - {product_name} ({self.quantity})"

    def save(self, *args, **kwargs):
        from decimal import Decimal

        # Auto-fill description from product if not provided
        if not self.description and self.product:
            self.description = self.product.name

        # Auto-fill unit price from pricing module if not provided
        if not self.unit_price and self.product and self.sales_order:
            self.unit_price = self.sales_order.get_product_price(
                self.product,
                self.sales_order.customer,
                self.quantity
            )

        # Ensure all numeric fields are Decimal
        self.quantity = Decimal(str(self.quantity))
        self.unit_price = Decimal(str(self.unit_price))
        self.discount_percent = Decimal(str(self.discount_percent))
        self.tax_rate = Decimal(str(self.tax_rate))
        self.quantity_delivered = Decimal(str(self.quantity_delivered or 0))

        # Calculate line total
        subtotal = self.quantity * self.unit_price
        discount_amount = subtotal * (self.discount_percent / 100)
        self.line_total = subtotal - discount_amount

        # Calculate tax
        if self.taxable:
            self.tax_amount = self.line_total * (self.tax_rate / 100)
        else:
            self.tax_amount = Decimal('0.00')

        # Calculate pending quantity
        self.quantity_pending = self.quantity - self.quantity_delivered

        super().save(*args, **kwargs)

        # Update parent sales order totals
        if self.sales_order:
            self.sales_order.calculate_totals()
            self.sales_order.save()


class CustomerBill(models.Model):
    """Customer Bill model for Accounts Receivable - comprehensive ERP implementation"""

    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('posted', 'Posted'),
        ('paid', 'Paid'),
        ('cancel', 'Cancelled'),  # Odoo-style state management
    ]

    BILL_TYPE_CHOICES = [
        ('bill', 'Bill'),
        ('credit', 'Credit'),
    ]

    # Basic Information
    bill_id = models.UUIDField(default=uuid.uuid4, unique=True)
    bill_number = models.CharField(max_length=50, unique=True, help_text="Unique identifier (e.g., 'CBILL-2023-001')")
    customer = models.ForeignKey('contacts.Contact', on_delete=models.SET_NULL, null=True, blank=True, related_name='customer_bills', help_text='Link to customer master')
    bill_type = models.CharField(max_length=10, choices=BILL_TYPE_CHOICES, default='bill', help_text="Type of bill - regular bill or credit")

    # Odoo-style AccountMove integration
    move_id = models.OneToOneField(
        'gl.AccountMove',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='customer_bill',
        help_text="Linked account move for GL integration"
    )

    # Dates
    bill_date = models.DateField(help_text="Date the bill was issued")
    due_date = models.DateField(help_text="Date the bill is due for payment")

    # Related Documents - for different bill creation scenarios
    sales_order = models.ForeignKey(SalesOrder, on_delete=models.SET_NULL, null=True, blank=True, related_name='customer_bills', help_text='Link to sales order if created from SO')
    delivery_note = models.ForeignKey(
        'GoodsDeliveryNote',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='customer_bills',
        help_text="Linked delivery note for goods delivery bills"
    )
    delivery_return_note = models.ForeignKey(
        'GoodsDeliveryReturnNote',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='customer_bills',
        help_text="Linked delivery return note for return-based bills"
    )

    # Financial Information
    subtotal = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    amount_paid = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    balance_due = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)

    # Status and Terms
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    payment_terms = models.CharField(max_length=100, blank=True, null=True)

    # Additional Information
    reference_number = models.CharField(max_length=100, blank=True, null=True, help_text="Customer's reference number")
    notes = models.TextField(blank=True, null=True, help_text="Internal notes")
    source_type = models.CharField(
        max_length=20,
        choices=[
            ('manual', 'Manual'),
            ('sales_order', 'Sales Order'),
            ('delivery_note', 'Delivery Note'),
            ('return_note', 'Return Note'),
        ],
        default='manual',
        help_text="Source document type for bill creation"
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='customer_bills_created')

    class Meta:
        db_table = 'sales_customer_bills'
        ordering = ['-bill_date', '-created_at']
        verbose_name = 'Customer Bill'
        verbose_name_plural = 'Customer Bills'

    def __str__(self):
        customer_name = self.customer.name if self.customer else "No Customer"
        return f"Bill {self.bill_number} - {customer_name}"

    def calculate_totals(self):
        """Calculate totals from line items"""
        from decimal import Decimal

        line_items = self.line_items.all()

        # Calculate subtotal from line items
        self.subtotal = sum(item.line_total for item in line_items) or Decimal('0.00')

        # Calculate tax amount from line items
        self.tax_amount = sum(item.tax_amount for item in line_items) or Decimal('0.00')

        # Calculate total amount
        self.total_amount = self.subtotal + self.tax_amount

        # Calculate balance due
        self.balance_due = self.total_amount - Decimal(str(self.amount_paid))

    def save(self, *args, **kwargs):
        from decimal import Decimal
        from gl.models import AccountMove, IrSequence, AccountJournal

        # Auto-generate bill number using Odoo-style sequence if not provided
        if not self.bill_number:
            try:
                # Try to get sequence for customer bills
                sequence = IrSequence.objects.get(code='customer.bill')
                self.bill_number = sequence.next_by_id(self.bill_date)
            except IrSequence.DoesNotExist:
                # Fallback to old method
                last_bill = CustomerBill.objects.filter(
                    bill_number__startswith='CBILL-'
                ).order_by('-created_at').first()

                if last_bill:
                    try:
                        last_number = int(last_bill.bill_number.split('-')[1])
                        new_number = last_number + 1
                    except (ValueError, IndexError):
                        new_number = 1
                else:
                    new_number = 1

                self.bill_number = f'CBILL-{new_number:06d}'

        # Ensure all decimal fields are Decimal type
        self.subtotal = Decimal(str(self.subtotal))
        self.tax_amount = Decimal(str(self.tax_amount))
        self.total_amount = Decimal(str(self.total_amount))
        self.amount_paid = Decimal(str(self.amount_paid))

        # Calculate balance due
        self.balance_due = self.total_amount - self.amount_paid

        # Create or update AccountMove (Odoo approach)
        if not self.move_id:
            # Determine move_type based on bill_type
            move_type = 'out_refund' if self.bill_type == 'credit' else 'out_invoice'

            try:
                # Get sales journal
                journal = AccountJournal.objects.get(type='sale')
            except AccountJournal.DoesNotExist:
                # Create default sales journal if it doesn't exist
                from gl.models import Account
                try:
                    default_account = Account.objects.filter(
                        account_type__type='REVENUE'
                    ).first()
                    if default_account:
                        journal = AccountJournal.objects.create(
                            name='Sales Journal',
                            code='SAL',
                            type='sale',
                            default_account_id=default_account
                        )
                    else:
                        journal = None
                except:
                    journal = None

            if journal:
                # Create AccountMove
                self.move_id = AccountMove.objects.create(
                    name='/',  # Will be set by sequence
                    move_type=move_type,
                    state='draft',
                    ref=self.reference_number or '',
                    date=self.bill_date,
                    partner_id=self.customer,
                    amount_total=self.total_amount,
                    amount_residual=self.balance_due,
                    journal_id=journal,
                    created_uid=getattr(self, '_created_by', None)
                )

        # Save first to ensure primary key exists
        super().save(*args, **kwargs)

        # Update AccountMove when bill is posted (Odoo approach)
        if self.status == 'posted' and self.move_id and self.move_id.state == 'draft':
            self._create_move_lines()
            self.move_id.action_post()

    def _create_move_lines(self):
        """
        Create AccountMoveLine entries (Odoo approach)
        This replaces the old create_gl_entries method
        """
        if not self.move_id:
            return

        from gl.models import AccountMoveLine, Account
        from decimal import Decimal

        # Clear existing lines
        self.move_id.line_ids.all().delete()

        # Get accounts
        try:
            # Receivable account (Asset account for customer receivables)
            receivable_account = Account.objects.filter(
                account_type__type='ASSET',
                detail_type__name__icontains='receivable'
            ).first()

            if not receivable_account:
                # Fallback to any asset account
                receivable_account = Account.objects.filter(
                    account_type__type='ASSET'
                ).first()

            # Revenue account (Revenue account for sales)
            revenue_account = Account.objects.filter(
                account_type__type='REVENUE'
            ).first()

            # Tax account (Liability account for output tax)
            tax_account = Account.objects.filter(
                account_type__type='LIABILITY',
                account_name__icontains='tax'
            ).first()

            if not receivable_account or not revenue_account:
                raise ValueError("Required accounts not found")

        except Exception as e:
            print(f"Error getting accounts: {e}")
            return

        is_credit = self.bill_type == 'credit'

        # Create receivable line (Customer owes us money)
        receivable_amount = abs(self.total_amount)
        AccountMoveLine.objects.create(
            move_id=self.move_id,
            account_id=receivable_account,
            partner_id=self.customer,
            name=f"Customer Bill {self.bill_number}",
            debit=receivable_amount if not is_credit else Decimal('0.00'),
            credit=Decimal('0.00') if not is_credit else receivable_amount,
        )

        # Create revenue lines from line items
        for line_item in self.line_items.all():
            line_amount = abs(line_item.line_total)
            if line_amount > 0:
                AccountMoveLine.objects.create(
                    move_id=self.move_id,
                    account_id=revenue_account,
                    partner_id=self.customer,
                    product_id=line_item.product,
                    quantity=line_item.quantity,
                    price_unit=line_item.unit_price,
                    name=line_item.item_description,
                    debit=Decimal('0.00') if not is_credit else line_amount,
                    credit=line_amount if not is_credit else Decimal('0.00'),
                )

        # Create tax line if there's tax
        if abs(self.tax_amount) > 0 and tax_account:
            tax_amount = abs(self.tax_amount)
            AccountMoveLine.objects.create(
                move_id=self.move_id,
                account_id=tax_account,
                partner_id=self.customer,
                name=f"Output Tax - {self.bill_number}",
                debit=Decimal('0.00') if not is_credit else tax_amount,
                credit=tax_amount if not is_credit else Decimal('0.00'),
            )

    def delete(self, *args, **kwargs):
        """Override delete to prevent deletion of posted bills"""
        from django.core.exceptions import ValidationError
        if self.status == 'posted':
            raise ValidationError("Posted customer bills cannot be deleted. Only draft bills can be deleted.")
        super().delete(*args, **kwargs)

    def create_gl_entries(self):
        """Create General Ledger entries for the customer bill or credit"""
        try:
            from gl.models import JournalEntry, JournalEntryLine, Account
            from decimal import Decimal

            # Determine entry type and reference prefix based on bill type
            is_credit = self.bill_type == 'credit'
            entry_prefix = "CC" if is_credit else "CB"
            entry_type_desc = "Customer Credit" if is_credit else "Customer Bill"
            source_doc_type = "CREDIT" if is_credit else "BILL"

            # Check if GL entries already exist for this bill
            existing_entry = JournalEntry.objects.filter(
                reference_number=f"{entry_prefix}-{self.bill_number}",
                entry_type='customer_bill'
            ).first()

            if existing_entry:
                return existing_entry  # Already created

            # Get the user who created this bill (or use a default system user)
            created_by_user = getattr(self, 'created_by', None)
            if not created_by_user:
                # Try to get the first admin user as fallback
                from django.contrib.auth.models import User
                created_by_user = User.objects.filter(is_superuser=True).first()
                if not created_by_user:
                    created_by_user = User.objects.first()

            # Create the main journal entry
            journal_entry = JournalEntry.objects.create(
                entry_number=f"{entry_prefix}-{self.bill_number}",
                reference_number=f"{entry_prefix}-{self.bill_number}",
                description=f"{entry_type_desc}: {self.customer.name} - {self.bill_number}",
                transaction_date=self.bill_date,
                entry_type='GENERAL',
                source_document_type=source_doc_type,
                source_document_id=str(self.id),
                source_document_reference=self.bill_number,
                status='DRAFT',
                created_by=created_by_user
            )

            # Get or create default accounts
            accounts_receivable_account = self.get_or_create_accounts_receivable_account()
            sales_tax_payable_account = self.get_or_create_sales_tax_payable_account()

            # Create journal lines for each line item
            # For regular bills: Debit accounts receivable, Credit revenue accounts
            # For credit bills: Credit accounts receivable, Debit revenue accounts (reverse)
            line_counter = 1  # Use sequential line numbers to avoid conflicts

            line_items = self.line_items.all()

            if line_items.exists():
                # Process line items
                for line_item in line_items:
                    if abs(line_item.line_total) > 0:  # Use absolute value for credit bills
                        # Get revenue account for this line item
                        revenue_account = self.get_revenue_account_for_line_item(line_item)

                        # Use absolute value of line total for journal entries
                        line_amount = abs(line_item.line_total)

                    if is_credit:
                        # For credit bills: Debit the revenue account (reduce revenue)
                        JournalEntryLine.objects.create(
                            journal_entry=journal_entry,
                            account=revenue_account,
                            description=f"Credit: {line_item.item_description}",
                            debit_amount=line_amount,
                            credit_amount=Decimal('0.00'),
                            line_number=line_counter
                        )
                    else:
                        # For regular bills: Credit the revenue account
                        JournalEntryLine.objects.create(
                            journal_entry=journal_entry,
                            account=revenue_account,
                            description=f"{line_item.item_description}",
                            debit_amount=Decimal('0.00'),
                            credit_amount=line_amount,
                            line_number=line_counter
                        )

                    line_counter += 1

                    # Create tax entry if there's tax on this line item
                    if abs(line_item.tax_amount) > 0:
                        tax_amount = abs(line_item.tax_amount)
                        tax_desc = f"Output Tax Credit - {line_item.item_description}" if is_credit else f"Output Tax - {line_item.item_description}"

                        if is_credit:
                            # For credit bills: Debit the tax account
                            JournalEntryLine.objects.create(
                                journal_entry=journal_entry,
                                account=sales_tax_payable_account,
                                description=tax_desc,
                                debit_amount=tax_amount,
                                credit_amount=Decimal('0.00'),
                                line_number=line_counter
                            )
                        else:
                            # For regular bills: Credit the tax account
                            JournalEntryLine.objects.create(
                                journal_entry=journal_entry,
                                account=sales_tax_payable_account,
                                description=tax_desc,
                                debit_amount=Decimal('0.00'),
                                credit_amount=tax_amount,
                                line_number=line_counter
                            )

                        line_counter += 1
            else:
                # No line items - create a single revenue entry for the subtotal
                revenue_account = self.get_or_create_default_revenue_account()
                revenue_amount = abs(self.subtotal) if self.subtotal else abs(self.total_amount)

                if is_credit:
                    # For credit bills: Debit the revenue account (reduce revenue)
                    JournalEntryLine.objects.create(
                        journal_entry=journal_entry,
                        account=revenue_account,
                        description=f"Credit: Customer Bill {self.bill_number}",
                        debit_amount=revenue_amount,
                        credit_amount=Decimal('0.00'),
                        line_number=line_counter
                    )
                else:
                    # For regular bills: Credit the revenue account
                    JournalEntryLine.objects.create(
                        journal_entry=journal_entry,
                        account=revenue_account,
                        description=f"Customer Bill {self.bill_number}",
                        debit_amount=Decimal('0.00'),
                        credit_amount=revenue_amount,
                        line_number=line_counter
                    )

                line_counter += 1

                # Create tax entry if there's tax
                if abs(self.tax_amount) > 0:
                    tax_amount = abs(self.tax_amount)
                    tax_desc = f"Output Tax Credit - {self.bill_number}" if is_credit else f"Output Tax - {self.bill_number}"

                    if is_credit:
                        # For credit bills: Debit the tax account
                        JournalEntryLine.objects.create(
                            journal_entry=journal_entry,
                            account=sales_tax_payable_account,
                            description=tax_desc,
                            debit_amount=tax_amount,
                            credit_amount=Decimal('0.00'),
                            line_number=line_counter
                        )
                    else:
                        # For regular bills: Credit the tax account
                        JournalEntryLine.objects.create(
                            journal_entry=journal_entry,
                            account=sales_tax_payable_account,
                            description=tax_desc,
                            debit_amount=Decimal('0.00'),
                            credit_amount=tax_amount,
                            line_number=line_counter
                        )

                    line_counter += 1

            # Increment line counter for the final accounts receivable entry
            line_counter += 1

            # Handle Accounts Receivable entry
            total_amount = abs(self.total_amount)  # Use absolute value
            ar_description = f"Accounts Receivable Credit - {self.customer.name}" if is_credit else f"Accounts Receivable - {self.customer.name}"

            if is_credit:
                # For credit bills: Credit Accounts Receivable (reduce asset)
                JournalEntryLine.objects.create(
                    journal_entry=journal_entry,
                    account=accounts_receivable_account,
                    description=ar_description,
                    debit_amount=Decimal('0.00'),
                    credit_amount=total_amount,
                    line_number=line_counter  # Use sequential line number
                )
            else:
                # For regular bills: Debit Accounts Receivable (increase asset)
                JournalEntryLine.objects.create(
                    journal_entry=journal_entry,
                    account=accounts_receivable_account,
                    description=ar_description,
                    debit_amount=total_amount,
                    credit_amount=Decimal('0.00'),
                    line_number=line_counter  # Use sequential line number
                )

            # Post the journal entry
            journal_entry.status = 'POSTED'
            journal_entry.save()

            return journal_entry

        except Exception as e:
            # Log the error but don't fail the save
            print(f"Error creating GL entries for customer bill {self.bill_number}: {e}")
            return None

    def get_revenue_account_for_line_item(self, line_item):
        """Get the appropriate revenue account for a line item"""
        try:
            from gl.models import Account

            # Try to get account from line item's account_code
            if line_item.account_code:
                try:
                    return Account.objects.get(account_number=line_item.account_code)
                except Account.DoesNotExist:
                    pass

            # Try to get account from product
            if line_item.product and hasattr(line_item.product, 'income_account_gl'):
                if line_item.product.income_account_gl:
                    return line_item.product.income_account_gl

            # Default to sales revenue account
            return self.get_or_create_default_revenue_account()

        except Exception as e:
            print(f"Error getting revenue account for line item: {e}")
            return self.get_or_create_default_revenue_account()

        except Exception:
            # Ultimate fallback
            from gl.models import Account
            return Account.objects.filter(account_type='REVENUE').first()

    def get_or_create_accounts_receivable_account(self):
        """Get or create the Accounts Receivable account"""
        from gl.models import Account, AccountType, DetailType

        try:
            # Try to find existing Accounts Receivable account
            ar_account = Account.objects.filter(
                account_name__icontains='Accounts Receivable'
            ).first()

            if ar_account:
                return ar_account

            # Create Accounts Receivable account if it doesn't exist
            asset_type = AccountType.objects.filter(name='Assets').first()
            if not asset_type:
                asset_type = AccountType.objects.create(
                    name='Assets',
                    normal_balance='DEBIT'
                )

            current_asset_detail = DetailType.objects.filter(
                name='Current Assets'
            ).first()
            if not current_asset_detail:
                current_asset_detail = DetailType.objects.create(
                    name='Current Assets',
                    account_type=asset_type
                )

            # Get a user for account creation
            from django.contrib.auth.models import User
            user = User.objects.filter(is_superuser=True).first() or User.objects.first()

            ar_account = Account.objects.create(
                account_number='1200',
                account_name='Accounts Receivable',
                account_type=asset_type,
                detail_type=current_asset_detail,
                description='Customer receivables and outstanding invoices',
                created_by=user
            )

            return ar_account

        except Exception as e:
            print(f"Error getting/creating Accounts Receivable account: {e}")
            return None

    def get_or_create_sales_tax_payable_account(self):
        """Get or create the Sales Tax Payable account for output tax"""
        from gl.models import Account, AccountType, DetailType

        try:
            # Try to find existing Sales Tax Payable account
            tax_account = Account.objects.filter(
                account_name__icontains='Sales Tax'
            ).first()

            if tax_account:
                return tax_account

            # Create Sales Tax Payable account if it doesn't exist
            liability_type = AccountType.objects.filter(name='Liabilities').first()
            if not liability_type:
                liability_type = AccountType.objects.create(
                    name='Liabilities',
                    normal_balance='CREDIT'
                )

            current_liability_detail = DetailType.objects.filter(
                name='Current Liabilities'
            ).first()
            if not current_liability_detail:
                current_liability_detail = DetailType.objects.create(
                    name='Current Liabilities',
                    account_type=liability_type
                )

            # Get a user for account creation
            from django.contrib.auth.models import User
            user = User.objects.filter(is_superuser=True).first() or User.objects.first()

            tax_account = Account.objects.create(
                account_number='2200',
                account_name='Sales Tax Payable',
                account_type=liability_type,
                detail_type=current_liability_detail,
                description='Output tax collected from customers',
                created_by=user
            )

            return tax_account

        except Exception as e:
            print(f"Error getting/creating Sales Tax Payable account: {e}")
            return None

    def get_or_create_default_revenue_account(self):
        """Get or create a default revenue account"""
        from gl.models import Account, AccountType, DetailType

        try:
            # Try to find existing Sales Revenue account
            revenue_account = Account.objects.filter(
                account_name__icontains='Sales Revenue'
            ).first()

            if revenue_account:
                return revenue_account

            # Create Sales Revenue account if it doesn't exist
            revenue_type = AccountType.objects.filter(name='Revenue').first()
            if not revenue_type:
                revenue_type = AccountType.objects.create(
                    name='Revenue',
                    normal_balance='CREDIT'
                )

            sales_detail = DetailType.objects.filter(
                name='Sales Revenue'
            ).first()
            if not sales_detail:
                sales_detail = DetailType.objects.create(
                    name='Sales Revenue',
                    account_type=revenue_type
                )

            # Get a user for account creation
            from django.contrib.auth.models import User
            user = User.objects.filter(is_superuser=True).first() or User.objects.first()

            revenue_account = Account.objects.create(
                account_number='4000',
                account_name='Sales Revenue',
                account_type=revenue_type,
                detail_type=sales_detail,
                description='Revenue from sales to customers',
                created_by=user
            )

            return revenue_account

        except Exception as e:
            print(f"Error getting/creating Sales Revenue account: {e}")
            return None




class GoodsDeliveryNote(models.Model):
    """Goods Delivery Note model for tracking deliveries to customers"""

    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('confirmed', 'Confirmed'),
        ('delivered', 'Delivered'),
        ('returned', 'Returned'),
        ('cancelled', 'Cancelled'),
    ]

    # Basic Information
    gdn_id = models.UUIDField(default=uuid.uuid4, unique=True)
    gdn_number = models.CharField(max_length=50, unique=True)
    sales_order = models.ForeignKey(SalesOrder, on_delete=models.CASCADE, related_name='delivery_notes')
    customer = models.ForeignKey('contacts.Contact', on_delete=models.SET_NULL, null=True, blank=True, related_name='delivery_notes')

    # Dates
    delivery_date = models.DateField()
    expected_delivery_date = models.DateField(blank=True, null=True)
    actual_delivery_date = models.DateField(blank=True, null=True)

    # Delivery Information
    delivery_address = models.TextField(blank=True, null=True)
    delivery_contact_person = models.CharField(max_length=200, blank=True, null=True)
    delivery_contact_phone = models.CharField(max_length=20, blank=True, null=True)

    # Vehicle/Transport Information
    vehicle_number = models.CharField(max_length=50, blank=True, null=True)
    driver_name = models.CharField(max_length=200, blank=True, null=True)
    driver_phone = models.CharField(max_length=20, blank=True, null=True)

    # Status and Notes
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    notes = models.TextField(blank=True, null=True)
    internal_notes = models.TextField(blank=True, null=True)

    # Signature and Confirmation
    customer_signature = models.TextField(blank=True, null=True, help_text="Customer signature or confirmation")
    received_by = models.CharField(max_length=200, blank=True, null=True)
    received_date = models.DateTimeField(blank=True, null=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='delivery_notes_created')

    class Meta:
        db_table = 'sales_goods_delivery_notes'
        ordering = ['-delivery_date', '-created_at']
        verbose_name = 'Goods Delivery Note'
        verbose_name_plural = 'Goods Delivery Notes'

    def __str__(self):
        customer_name = self.customer.name if self.customer else "No Customer"
        return f"GDN {self.gdn_number} - {customer_name}"

    def save(self, *args, **kwargs):
        # Auto-generate GDN number if not provided
        if not self.gdn_number:
            last_gdn = GoodsDeliveryNote.objects.filter(
                gdn_number__startswith='GDN-'
            ).order_by('-created_at').first()

            if last_gdn:
                try:
                    last_number = int(last_gdn.gdn_number.split('-')[1])
                    new_number = last_number + 1
                except (ValueError, IndexError):
                    new_number = 1
            else:
                new_number = 1

            self.gdn_number = f'GDN-{new_number:06d}'

        # Auto-set customer from sales order if not provided
        if not self.customer and self.sales_order:
            self.customer = self.sales_order.customer

        super().save(*args, **kwargs)

        # Update inventory when status changes to delivered
        if self.status == 'delivered':
            self.update_inventory_on_delivery()

    def update_inventory_on_delivery(self):
        """Update inventory when goods are delivered"""
        try:
            from inventory.models import InventoryTransaction

            for line_item in self.line_items.all():
                if line_item.quantity_delivered > 0:
                    # Create inventory transaction for stock reduction
                    InventoryTransaction.objects.create(
                        product=line_item.product,
                        transaction_type='SALE',
                        quantity=-line_item.quantity_delivered,  # Negative for stock reduction
                        unit_cost=line_item.product.cost_price if line_item.product else 0,
                        reference_document_type='GDN',
                        reference_document_id=str(self.id),
                        reference_document_number=self.gdn_number,
                        transaction_date=self.delivery_date,
                        notes=f"Stock reduction for delivery - {self.gdn_number}",
                        created_by=self.created_by
                    )
        except Exception as e:
            print(f"Error updating inventory for GDN {self.gdn_number}: {e}")


class GoodsDeliveryNoteLineItem(models.Model):
    """Goods Delivery Note line items"""

    delivery_note = models.ForeignKey(GoodsDeliveryNote, on_delete=models.CASCADE, related_name='line_items')
    sales_order_line_item = models.ForeignKey(SalesOrderLineItem, on_delete=models.CASCADE, related_name='delivery_line_items')

    # Product Information (copied from sales order line item)
    product = models.ForeignKey('Pricing.Product', on_delete=models.SET_NULL, null=True, blank=True, related_name='delivery_line_items')
    description = models.TextField()

    # Quantities
    quantity_ordered = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    quantity_delivered = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    quantity_remaining = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)

    # Unit Information
    unit_of_measure = models.CharField(max_length=20, default='pcs')
    unit_price = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)

    # Line Information
    line_order = models.PositiveIntegerField(default=1)
    notes = models.TextField(blank=True, null=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'sales_goods_delivery_note_line_items'
        ordering = ['line_order', 'created_at']
        unique_together = ['delivery_note', 'sales_order_line_item']

    def __str__(self):
        product_name = self.product.name if self.product else "Custom Item"
        return f"{self.delivery_note.gdn_number} - {product_name}"

    def save(self, *args, **kwargs):
        from decimal import Decimal

        # Auto-fill from sales order line item if not provided
        if self.sales_order_line_item:
            if not self.product:
                self.product = self.sales_order_line_item.product
            if not self.description:
                self.description = self.sales_order_line_item.description
            if not self.unit_of_measure:
                self.unit_of_measure = self.sales_order_line_item.unit_of_measure
            if not self.unit_price:
                self.unit_price = self.sales_order_line_item.unit_price
            if not self.quantity_ordered:
                self.quantity_ordered = self.sales_order_line_item.quantity

        # Ensure all numeric fields are Decimal
        self.quantity_ordered = Decimal(str(self.quantity_ordered))
        self.quantity_delivered = Decimal(str(self.quantity_delivered))
        self.unit_price = Decimal(str(self.unit_price))

        # Calculate remaining quantity
        self.quantity_remaining = self.quantity_ordered - self.quantity_delivered

        super().save(*args, **kwargs)

        # Update sales order line item delivery status
        if self.sales_order_line_item:
            self.sales_order_line_item.quantity_delivered = self.quantity_delivered
            self.sales_order_line_item.save()


class CustomerInvoice(models.Model):
    """Customer Invoice model for billing customers"""

    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('sent', 'Sent'),
        ('viewed', 'Viewed'),
        ('partial', 'Partially Paid'),
        ('paid', 'Paid'),
        ('overdue', 'Overdue'),
        ('cancelled', 'Cancelled'),
    ]

    INVOICE_TYPE_CHOICES = [
        ('invoice', 'Invoice'),
        ('credit', 'Credit Note'),
    ]

    # Basic Information
    invoice_id = models.UUIDField(default=uuid.uuid4, unique=True)
    invoice_number = models.CharField(max_length=50, unique=True)
    customer = models.ForeignKey('contacts.Contact', on_delete=models.SET_NULL, null=True, blank=True, related_name='customer_invoices')

    # Source Information
    sales_order = models.ForeignKey(SalesOrder, on_delete=models.SET_NULL, null=True, blank=True, related_name='invoices')
    delivery_note = models.ForeignKey(GoodsDeliveryNote, on_delete=models.SET_NULL, null=True, blank=True, related_name='invoices')

    # Invoice Type and Status
    invoice_type = models.CharField(max_length=20, choices=INVOICE_TYPE_CHOICES, default='invoice')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')

    # Dates
    invoice_date = models.DateField()
    due_date = models.DateField()
    payment_date = models.DateField(blank=True, null=True)

    # Financial Information
    subtotal = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    discount_percent = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    discount_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    shipping_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    amount_paid = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    balance_due = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)

    # Payment Information
    payment_terms = models.CharField(max_length=20, blank=True, null=True)
    payment_method = models.CharField(max_length=50, blank=True, null=True)

    # Additional Information
    reference_number = models.CharField(max_length=100, blank=True, null=True)
    memo = models.TextField(blank=True, null=True, help_text="Internal memo")
    notes = models.TextField(blank=True, null=True, help_text="Notes to customer")

    # Billing Address
    billing_address = models.TextField(blank=True, null=True)

    # Email Tracking
    email_sent = models.BooleanField(default=False)
    email_sent_date = models.DateTimeField(blank=True, null=True)
    email_viewed = models.BooleanField(default=False)
    email_viewed_date = models.DateTimeField(blank=True, null=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='customer_invoices_created')

    class Meta:
        db_table = 'sales_customer_invoices'
        ordering = ['-invoice_date', '-created_at']
        verbose_name = 'Customer Invoice'
        verbose_name_plural = 'Customer Invoices'

    def __str__(self):
        customer_name = self.customer.name if self.customer else "No Customer"
        return f"Invoice {self.invoice_number} - {customer_name}"

    def save(self, *args, **kwargs):
        # Auto-generate invoice number if not provided
        if not self.invoice_number:
            if self.invoice_type == 'credit':
                prefix = 'CN-'
                last_invoice = CustomerInvoice.objects.filter(
                    invoice_number__startswith=prefix
                ).order_by('-created_at').first()
            else:
                prefix = 'INV-'
                last_invoice = CustomerInvoice.objects.filter(
                    invoice_number__startswith=prefix
                ).order_by('-created_at').first()

            if last_invoice:
                try:
                    last_number = int(last_invoice.invoice_number.split('-')[1])
                    new_number = last_number + 1
                except (ValueError, IndexError):
                    new_number = 1
            else:
                new_number = 1

            self.invoice_number = f'{prefix}{new_number:06d}'

        # Auto-set customer from sales order or delivery note if not provided
        if not self.customer:
            if self.sales_order:
                self.customer = self.sales_order.customer
            elif self.delivery_note:
                self.customer = self.delivery_note.customer

        # Calculate balance due
        self.balance_due = self.total_amount - self.amount_paid

        super().save(*args, **kwargs)

        # Create GL entries when invoice is finalized
        if self.status in ['sent', 'viewed', 'partial', 'paid'] and self.invoice_type == 'invoice':
            self.create_gl_entries()
        elif self.status in ['sent', 'viewed'] and self.invoice_type == 'credit':
            self.create_credit_gl_entries()

    def calculate_totals(self):
        """Calculate invoice totals from line items"""
        from decimal import Decimal

        line_items = self.line_items.all()

        # Calculate subtotal
        self.subtotal = sum(
            item.quantity * item.unit_price
            for item in line_items
        )

        # Apply discount
        if self.discount_percent > 0:
            self.discount_amount = (self.subtotal * self.discount_percent) / Decimal('100')

        # Calculate total before tax
        total_before_tax = self.subtotal - self.discount_amount

        # Calculate tax (assuming tax is calculated on discounted amount)
        # Tax calculation will be enhanced when integrated with tax settings

        # Calculate final total
        self.total_amount = total_before_tax + self.tax_amount + self.shipping_amount

        # Calculate balance due
        self.balance_due = self.total_amount - self.amount_paid

        self.save()

    def create_gl_entries(self):
        """Create GL journal entries for customer invoice"""
        try:
            from gl.models import JournalEntry, JournalEntryLine
            from decimal import Decimal

            # Create journal entry
            journal_entry = JournalEntry.objects.create(
                entry_date=self.invoice_date,
                reference_number=f"Customer Invoice - {self.invoice_number}",
                description=f"Customer Invoice {self.invoice_number} - {self.customer.name if self.customer else 'No Customer'}",
                total_amount=self.total_amount,
                created_by=self.created_by
            )

            # Debit: Accounts Receivable
            JournalEntryLine.objects.create(
                journal_entry=journal_entry,
                account_code='1200-AR',  # Accounts Receivable
                account_name='Accounts Receivable',
                debit_amount=self.total_amount,
                credit_amount=Decimal('0.00'),
                description=f"AR for Invoice {self.invoice_number}"
            )

            # Credit: Sales Revenue
            if self.subtotal > 0:
                JournalEntryLine.objects.create(
                    journal_entry=journal_entry,
                    account_code='4000-SALES',  # Sales Revenue
                    account_name='Sales Revenue',
                    debit_amount=Decimal('0.00'),
                    credit_amount=self.subtotal - self.discount_amount,
                    description=f"Sales Revenue for Invoice {self.invoice_number}"
                )

            # Credit: Sales Tax Payable (if applicable)
            if self.tax_amount > 0:
                JournalEntryLine.objects.create(
                    journal_entry=journal_entry,
                    account_code='2200-STP',  # Sales Tax Payable
                    account_name='Sales Tax Payable',
                    debit_amount=Decimal('0.00'),
                    credit_amount=self.tax_amount,
                    description=f"Sales Tax for Invoice {self.invoice_number}"
                )

            return journal_entry

        except Exception as e:
            print(f"Error creating GL entries for invoice {self.invoice_number}: {e}")
            return None

    def create_credit_gl_entries(self):
        """Create GL journal entries for customer credit note"""
        try:
            from gl.models import JournalEntry, JournalEntryLine
            from decimal import Decimal

            # Create journal entry (reverse of invoice)
            journal_entry = JournalEntry.objects.create(
                entry_date=self.invoice_date,
                reference_number=f"Customer Credit Note - {self.invoice_number}",
                description=f"Customer Credit Note {self.invoice_number} - {self.customer.name if self.customer else 'No Customer'}",
                total_amount=self.total_amount,
                created_by=self.created_by
            )

            # Credit: Accounts Receivable (reduce customer balance)
            JournalEntryLine.objects.create(
                journal_entry=journal_entry,
                account_code='1200-AR',  # Accounts Receivable
                account_name='Accounts Receivable',
                debit_amount=Decimal('0.00'),
                credit_amount=self.total_amount,
                description=f"AR Credit for Credit Note {self.invoice_number}"
            )

            # Debit: Sales Returns and Allowances
            if self.subtotal > 0:
                JournalEntryLine.objects.create(
                    journal_entry=journal_entry,
                    account_code='4100-SRA',  # Sales Returns and Allowances
                    account_name='Sales Returns and Allowances',
                    debit_amount=self.subtotal - self.discount_amount,
                    credit_amount=Decimal('0.00'),
                    description=f"Sales Return for Credit Note {self.invoice_number}"
                )

            # Debit: Sales Tax Payable (if applicable)
            if self.tax_amount > 0:
                JournalEntryLine.objects.create(
                    journal_entry=journal_entry,
                    account_code='2200-STP',  # Sales Tax Payable
                    account_name='Sales Tax Payable',
                    debit_amount=self.tax_amount,
                    credit_amount=Decimal('0.00'),
                    description=f"Sales Tax Credit for Credit Note {self.invoice_number}"
                )

            return journal_entry

        except Exception as e:
            print(f"Error creating GL entries for credit note {self.invoice_number}: {e}")
            return None


class CustomerInvoiceLineItem(models.Model):
    """Customer Invoice line items"""

    invoice = models.ForeignKey(CustomerInvoice, on_delete=models.CASCADE, related_name='line_items')
    delivery_note_line_item = models.ForeignKey(GoodsDeliveryNoteLineItem, on_delete=models.SET_NULL, null=True, blank=True, related_name='invoice_line_items')

    # Product Information
    product = models.ForeignKey('Pricing.Product', on_delete=models.SET_NULL, null=True, blank=True, related_name='invoice_line_items')
    description = models.TextField()

    # Quantities and Pricing
    quantity = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    unit_of_measure = models.CharField(max_length=20, default='pcs')
    unit_price = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    line_total = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)

    # Discount Information
    discount_percent = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    discount_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)

    # Tax Information
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)

    # Chart of Accounts Integration
    account_code = models.CharField(max_length=20, blank=True, null=True, help_text="Revenue account code")
    account_name = models.CharField(max_length=200, blank=True, null=True, help_text="Revenue account name")

    # Line Information
    line_order = models.PositiveIntegerField(default=1)
    notes = models.TextField(blank=True, null=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'sales_customer_invoice_line_items'
        ordering = ['line_order', 'created_at']

    def __str__(self):
        product_name = self.product.name if self.product else "Custom Item"
        return f"{self.invoice.invoice_number} - {product_name}"

    def save(self, *args, **kwargs):
        from decimal import Decimal

        # Auto-fill from delivery note line item if not provided
        if self.delivery_note_line_item:
            if not self.product:
                self.product = self.delivery_note_line_item.product
            if not self.description:
                self.description = self.delivery_note_line_item.description
            if not self.unit_of_measure:
                self.unit_of_measure = self.delivery_note_line_item.unit_of_measure
            if not self.unit_price:
                self.unit_price = self.delivery_note_line_item.unit_price
            if not self.quantity:
                self.quantity = self.delivery_note_line_item.quantity_delivered

        # Ensure all numeric fields are Decimal
        self.quantity = Decimal(str(self.quantity))
        self.unit_price = Decimal(str(self.unit_price))
        self.discount_percent = Decimal(str(self.discount_percent))
        self.tax_rate = Decimal(str(self.tax_rate))

        # Calculate discount amount
        line_subtotal = self.quantity * self.unit_price
        if self.discount_percent > 0:
            self.discount_amount = (line_subtotal * self.discount_percent) / Decimal('100')
        else:
            self.discount_amount = Decimal('0.00')

        # Calculate tax amount
        taxable_amount = line_subtotal - self.discount_amount
        if self.tax_rate > 0:
            self.tax_amount = (taxable_amount * self.tax_rate) / Decimal('100')
        else:
            self.tax_amount = Decimal('0.00')

        # Calculate line total
        self.line_total = taxable_amount + self.tax_amount

        # Set default account code for sales revenue
        if not self.account_code:
            self.account_code = '4000-SALES'
            self.account_name = 'Sales Revenue'

        super().save(*args, **kwargs)

        # Update invoice totals
        if self.invoice:
            self.invoice.calculate_totals()


class GoodsDeliveryReturnNote(models.Model):
    """Goods Delivery Return Note model for tracking returns from customers"""

    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('confirmed', 'Confirmed'),
        ('received', 'Received'),
        ('processed', 'Processed'),
        ('cancelled', 'Cancelled'),
    ]

    RETURN_REASON_CHOICES = [
        ('defective', 'Defective Product'),
        ('wrong_item', 'Wrong Item Delivered'),
        ('damaged', 'Damaged in Transit'),
        ('customer_request', 'Customer Request'),
        ('quality_issue', 'Quality Issue'),
        ('other', 'Other'),
    ]

    # Basic Information
    gdrn_id = models.UUIDField(default=uuid.uuid4, unique=True)
    gdrn_number = models.CharField(max_length=50, unique=True)
    delivery_note = models.ForeignKey(GoodsDeliveryNote, on_delete=models.CASCADE, related_name='return_notes')
    customer = models.ForeignKey('contacts.Contact', on_delete=models.SET_NULL, null=True, blank=True, related_name='return_notes')

    # Dates
    return_date = models.DateField()
    expected_return_date = models.DateField(blank=True, null=True)
    actual_return_date = models.DateField(blank=True, null=True)

    # Return Information
    return_reason = models.CharField(max_length=50, choices=RETURN_REASON_CHOICES, default='customer_request')
    return_address = models.TextField(blank=True, null=True)
    return_contact_person = models.CharField(max_length=200, blank=True, null=True)
    return_contact_phone = models.CharField(max_length=20, blank=True, null=True)

    # Status and Notes
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    notes = models.TextField(blank=True, null=True)
    internal_notes = models.TextField(blank=True, null=True)

    # Quality Control
    quality_check_passed = models.BooleanField(default=False)
    quality_check_notes = models.TextField(blank=True, null=True)
    quality_checked_by = models.CharField(max_length=200, blank=True, null=True)
    quality_check_date = models.DateTimeField(blank=True, null=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='return_notes_created')

    class Meta:
        db_table = 'sales_goods_delivery_return_notes'
        ordering = ['-return_date', '-created_at']
        verbose_name = 'Goods Delivery Return Note'
        verbose_name_plural = 'Goods Delivery Return Notes'

    def __str__(self):
        customer_name = self.customer.name if self.customer else "No Customer"
        return f"GDRN {self.gdrn_number} - {customer_name}"

    def save(self, *args, **kwargs):
        # Auto-generate GDRN number if not provided
        if not self.gdrn_number:
            last_gdrn = GoodsDeliveryReturnNote.objects.filter(
                gdrn_number__startswith='GDRN-'
            ).order_by('-created_at').first()

            if last_gdrn:
                try:
                    last_number = int(last_gdrn.gdrn_number.split('-')[1])
                    new_number = last_number + 1
                except (ValueError, IndexError):
                    new_number = 1
            else:
                new_number = 1

            self.gdrn_number = f'GDRN-{new_number:06d}'

        # Auto-set customer from delivery note if not provided
        if not self.customer and self.delivery_note:
            self.customer = self.delivery_note.customer

        super().save(*args, **kwargs)

        # Update inventory when status changes to received
        if self.status == 'received':
            self.update_inventory_on_return()

    def update_inventory_on_return(self):
        """Update inventory when goods are returned"""
        try:
            from inventory.models import InventoryTransaction

            for line_item in self.line_items.all():
                if line_item.quantity_returned > 0:
                    # Create inventory transaction for stock increase
                    InventoryTransaction.objects.create(
                        product=line_item.product,
                        transaction_type='RETURN',
                        quantity=line_item.quantity_returned,  # Positive for stock increase
                        unit_cost=line_item.product.cost_price if line_item.product else 0,
                        reference_document_type='GDRN',
                        reference_document_id=str(self.id),
                        reference_document_number=self.gdrn_number,
                        transaction_date=self.return_date,
                        notes=f"Stock increase for return - {self.gdrn_number}",
                        created_by=self.created_by
                    )
        except Exception as e:
            print(f"Error updating inventory for GDRN {self.gdrn_number}: {e}")


class GoodsDeliveryReturnNoteLineItem(models.Model):
    """Goods Delivery Return Note line items"""

    return_note = models.ForeignKey(GoodsDeliveryReturnNote, on_delete=models.CASCADE, related_name='line_items')
    delivery_note_line_item = models.ForeignKey(GoodsDeliveryNoteLineItem, on_delete=models.CASCADE, related_name='return_line_items')

    # Product Information (copied from delivery note line item)
    product = models.ForeignKey('Pricing.Product', on_delete=models.SET_NULL, null=True, blank=True, related_name='return_line_items')
    description = models.TextField()

    # Quantities
    quantity_delivered = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    quantity_returned = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)

    # Unit Information
    unit_of_measure = models.CharField(max_length=20, default='pcs')
    unit_price = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)

    # Return Information
    return_reason = models.CharField(max_length=50, blank=True, null=True)
    condition = models.CharField(max_length=50, choices=[
        ('good', 'Good Condition'),
        ('damaged', 'Damaged'),
        ('defective', 'Defective'),
        ('expired', 'Expired'),
    ], default='good')

    # Line Information
    line_order = models.PositiveIntegerField(default=1)
    notes = models.TextField(blank=True, null=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'sales_goods_delivery_return_note_line_items'
        ordering = ['line_order', 'created_at']
        unique_together = ['return_note', 'delivery_note_line_item']

    def __str__(self):
        product_name = self.product.name if self.product else "Custom Item"
        return f"{self.return_note.gdrn_number} - {product_name}"

    def save(self, *args, **kwargs):
        from decimal import Decimal

        # Auto-fill from delivery note line item if not provided
        if self.delivery_note_line_item:
            if not self.product:
                self.product = self.delivery_note_line_item.product
            if not self.description:
                self.description = self.delivery_note_line_item.description
            if not self.unit_of_measure:
                self.unit_of_measure = self.delivery_note_line_item.unit_of_measure
            if not self.unit_price:
                self.unit_price = self.delivery_note_line_item.unit_price
            if not self.quantity_delivered:
                self.quantity_delivered = self.delivery_note_line_item.quantity_delivered

        # Ensure all numeric fields are Decimal
        self.quantity_delivered = Decimal(str(self.quantity_delivered))
        self.quantity_returned = Decimal(str(self.quantity_returned))
        self.unit_price = Decimal(str(self.unit_price))

        super().save(*args, **kwargs)


class CustomerBillItem(models.Model):
    """Customer Bill line items for products and services"""

    customer_bill = models.ForeignKey(CustomerBill, on_delete=models.CASCADE, related_name='line_items')

    # Product/Service Link
    product = models.ForeignKey(
        'sales.Product',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='customer_bill_items',
        help_text="Link to product master for GL integration"
    )

    # Item details
    item_description = models.TextField(help_text="Description of the item/service")
    quantity = models.DecimalField(max_digits=10, decimal_places=2, default=1.00)
    unit_price = models.DecimalField(max_digits=15, decimal_places=2, default=0.00, help_text="Sales price per unit")
    line_total = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)

    # Tax information
    taxable = models.BooleanField(default=True)
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)

    # Chart of Accounts integration
    account_code = models.CharField(max_length=50, blank=True, null=True, help_text="Revenue account code for GL posting")

    # Metadata
    line_order = models.PositiveIntegerField(default=1, help_text="Order of line item in the bill")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'sales_customer_bill_items'
        ordering = ['line_order', 'created_at']
        unique_together = ['customer_bill', 'line_order']

    def __str__(self):
        return f"{self.customer_bill.bill_number} - {self.item_description[:50]}"

    @property
    def product_name(self):
        """Get product name for display"""
        return self.product.name if self.product else None

    def save(self, *args, **kwargs):
        from decimal import Decimal

        # Auto-fill description from product if not provided
        if not self.item_description and self.product:
            self.item_description = self.product.name

        # Auto-fill account code from product if not provided
        if not self.account_code and self.product:
            # Use product's revenue account or default revenue account
            self.account_code = getattr(self.product, 'revenue_account_code', '4000-SALES')

        # Ensure all numeric fields are Decimal
        self.quantity = Decimal(str(self.quantity))
        self.unit_price = Decimal(str(self.unit_price))
        self.tax_rate = Decimal(str(self.tax_rate))

        # Calculate line total
        self.line_total = self.quantity * self.unit_price

        # Calculate tax
        if self.taxable:
            self.tax_amount = self.line_total * (self.tax_rate / 100)
        else:
            self.tax_amount = Decimal('0.00')

        super().save(*args, **kwargs)

        # Update parent customer bill totals
        if self.customer_bill:
            self.customer_bill.calculate_totals()
            self.customer_bill.save()
















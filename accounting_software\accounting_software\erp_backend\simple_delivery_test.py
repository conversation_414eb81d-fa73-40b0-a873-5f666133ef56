#!/usr/bin/env python3
"""
Simple test to verify delivery note creation works
"""
import requests
import json
from datetime import date, timed<PERSON>ta

def test_delivery_note():
    print("=== SIMPLE DELIVERY NOTE TEST ===")
    
    # Step 1: Get token
    try:
        login_response = requests.post(
            'http://localhost:8000/api-token-auth/',
            json={'username': 'admin', 'password': 'admin123'}
        )
        
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code}")
            return
        
        token = login_response.json()['token']
        print(f"✅ Got token: {token[:20]}...")
        
    except Exception as e:
        print(f"❌ Login error: {e}")
        return
    
    # Step 2: Create delivery note
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json'
    }
    
    data = {
        'sales_order': 20,
        'customer': 87,
        'delivery_date': str(date.today()),
        'expected_delivery_date': str(date.today() + timedelta(days=1)),
        'delivery_address': 'Test Address',
        'delivery_contact_person': '',
        'delivery_contact_phone': '',
        'vehicle_number': '',
        'driver_name': '',
        'driver_phone': '',
        'notes': '',
        'internal_notes': '',
        'status': 'draft'
    }
    
    try:
        response = requests.post(
            'http://localhost:8000/api/sales/delivery-notes/',
            headers=headers,
            json=data
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            print(f"✅ SUCCESS: {result['gdn_number']}")
        else:
            print(f"❌ FAILED: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == '__main__':
    test_delivery_note()

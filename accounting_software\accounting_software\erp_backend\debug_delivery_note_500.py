#!/usr/bin/env python3
"""
Debug script to identify the exact cause of 500 errors in delivery note creation
"""
import os
import sys
import django
import requests
import json
from datetime import date, timedelta

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

def test_all_scenarios():
    """Test various scenarios that might cause 500 errors"""
    print("=== DEBUGGING 500 ERRORS ===")
    
    # Get auth token
    token = get_token()
    if not token:
        print("❌ Cannot get auth token")
        return
    
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json'
    }
    
    # Test scenarios
    scenarios = [
        {
            'name': 'Valid data',
            'data': {
                'sales_order': 20,
                'customer': 87,
                'delivery_date': str(date.today()),
                'expected_delivery_date': str(date.today() + timedelta(days=1)),
                'delivery_address': 'Test Address',
                'status': 'draft'
            }
        },
        {
            'name': 'Empty strings (like frontend)',
            'data': {
                'sales_order': 20,
                'customer': 87,
                'delivery_date': str(date.today()),
                'expected_delivery_date': str(date.today() + timedelta(days=1)),
                'delivery_address': '',
                'delivery_contact_person': '',
                'delivery_contact_phone': '',
                'vehicle_number': '',
                'driver_name': '',
                'driver_phone': '',
                'notes': '',
                'internal_notes': '',
                'status': 'draft'
            }
        },
        {
            'name': 'Missing customer',
            'data': {
                'sales_order': 20,
                'delivery_date': str(date.today()),
                'status': 'draft'
            }
        },
        {
            'name': 'Invalid sales order',
            'data': {
                'sales_order': 99999,
                'customer': 87,
                'delivery_date': str(date.today()),
                'status': 'draft'
            }
        },
        {
            'name': 'Invalid customer',
            'data': {
                'sales_order': 20,
                'customer': 99999,
                'delivery_date': str(date.today()),
                'status': 'draft'
            }
        },
        {
            'name': 'Invalid date format',
            'data': {
                'sales_order': 20,
                'customer': 87,
                'delivery_date': 'invalid-date',
                'status': 'draft'
            }
        },
        {
            'name': 'Null values',
            'data': {
                'sales_order': 20,
                'customer': None,
                'delivery_date': str(date.today()),
                'status': 'draft'
            }
        }
    ]
    
    for scenario in scenarios:
        print(f"\n--- Testing: {scenario['name']} ---")
        test_scenario(headers, scenario['data'], scenario['name'])

def test_scenario(headers, data, name):
    """Test a specific scenario"""
    try:
        print(f"Data: {json.dumps(data, indent=2)}")
        
        response = requests.post(
            'http://localhost:8000/api/sales/delivery-notes/',
            headers=headers,
            json=data
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            print(f"✅ SUCCESS: Created {result.get('gdn_number')}")
        elif response.status_code == 400:
            error_data = response.json()
            print(f"⚠️  VALIDATION ERROR: {error_data}")
        elif response.status_code == 500:
            print(f"❌ SERVER ERROR (500): {response.text[:200]}...")
            # Try to get more details from Django logs
            print("This is the error we need to fix!")
        else:
            print(f"❌ OTHER ERROR ({response.status_code}): {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ REQUEST ERROR: {e}")

def get_token():
    """Get authentication token"""
    try:
        response = requests.post(
            'http://localhost:8000/api-token-auth/',
            json={'username': 'admin', 'password': 'admin123'}
        )
        if response.status_code == 200:
            return response.json().get('token')
    except:
        pass
    return None

def test_direct_model_creation():
    """Test direct model creation to isolate serializer vs model issues"""
    print("\n=== TESTING DIRECT MODEL CREATION ===")
    
    try:
        from sales.models import GoodsDeliveryNote, SalesOrder
        from contacts.models import Contact
        
        # Get existing sales order and customer
        so = SalesOrder.objects.first()
        customer = Contact.objects.first()
        
        if not so or not customer:
            print("❌ No sales order or customer found")
            return
        
        # Test direct model creation
        gdn_data = {
            'sales_order': so,
            'customer': customer,
            'delivery_date': date.today(),
            'status': 'draft'
        }
        
        gdn = GoodsDeliveryNote(**gdn_data)
        gdn.save()
        
        print(f"✅ Direct model creation successful: {gdn.gdn_number}")
        
        # Clean up
        gdn.delete()
        
    except Exception as e:
        print(f"❌ Direct model creation failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_all_scenarios()
    test_direct_model_creation()

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Delivery Note Frontend</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        input, select { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Delivery Note Frontend Test</h1>
        
        <div class="section">
            <h2>Step 1: Authentication</h2>
            <button onclick="testLogin()">Test Login</button>
            <button onclick="checkToken()">Check Stored Token</button>
            <div id="authResult"></div>
        </div>
        
        <div class="section">
            <h2>Step 2: Get Sales Orders</h2>
            <button onclick="getSalesOrders()">Get Sales Orders</button>
            <div id="salesOrderResult"></div>
        </div>
        
        <div class="section">
            <h2>Step 3: Create Delivery Note</h2>
            <label>Sales Order ID: <input type="number" id="salesOrderId" value="20"></label><br>
            <label>Customer ID: <input type="number" id="customerId" value="87"></label><br>
            <label>Delivery Address: <input type="text" id="deliveryAddress" value="Test Address"></label><br>
            <button onclick="createDeliveryNote()">Create Delivery Note</button>
            <div id="deliveryNoteResult"></div>
        </div>
        
        <div class="section">
            <h2>Debug Information</h2>
            <div id="debugInfo"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api';
        
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            element.innerHTML = `<div class="${className}"><pre>${JSON.stringify(message, null, 2)}</pre></div>`;
        }
        
        function debug(message) {
            const debugElement = document.getElementById('debugInfo');
            const timestamp = new Date().toLocaleTimeString();
            debugElement.innerHTML += `<div class="info">[${timestamp}] ${JSON.stringify(message, null, 2)}</div>`;
        }
        
        async function testLogin() {
            try {
                debug('Starting login test...');
                
                const response = await fetch('http://localhost:8000/api-token-auth/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                debug(`Login response status: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    localStorage.setItem('token', data.token);
                    log('authResult', {
                        status: 'SUCCESS',
                        token: data.token.substring(0, 20) + '...',
                        message: 'Login successful, token stored'
                    }, 'success');
                    debug('Token stored in localStorage');
                } else {
                    const errorData = await response.text();
                    log('authResult', {
                        status: 'FAILED',
                        statusCode: response.status,
                        error: errorData
                    }, 'error');
                    debug(`Login failed: ${response.status} - ${errorData}`);
                }
            } catch (error) {
                log('authResult', {
                    status: 'ERROR',
                    message: error.message
                }, 'error');
                debug(`Login error: ${error.message}`);
            }
        }
        
        function checkToken() {
            const token = localStorage.getItem('token');
            if (token) {
                log('authResult', {
                    status: 'TOKEN_FOUND',
                    token: token.substring(0, 20) + '...',
                    length: token.length
                }, 'success');
                debug('Token found in localStorage');
            } else {
                log('authResult', {
                    status: 'NO_TOKEN',
                    message: 'No token found in localStorage'
                }, 'error');
                debug('No token in localStorage');
            }
        }
        
        async function getSalesOrders() {
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    log('salesOrderResult', {
                        status: 'NO_TOKEN',
                        message: 'Please login first'
                    }, 'error');
                    return;
                }
                
                debug('Getting sales orders...');
                
                const response = await fetch(`${API_BASE}/sales/sales-orders/?page_size=5`, {
                    headers: {
                        'Authorization': `Token ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                debug(`Sales orders response status: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log('salesOrderResult', {
                        status: 'SUCCESS',
                        count: data.count,
                        results: data.results.map(so => ({
                            id: so.id,
                            so_number: so.so_number,
                            customer: so.customer,
                            customer_id: so.customer_id
                        }))
                    }, 'success');
                    debug('Sales orders retrieved successfully');
                } else {
                    const errorData = await response.text();
                    log('salesOrderResult', {
                        status: 'FAILED',
                        statusCode: response.status,
                        error: errorData
                    }, 'error');
                    debug(`Sales orders failed: ${response.status} - ${errorData}`);
                }
            } catch (error) {
                log('salesOrderResult', {
                    status: 'ERROR',
                    message: error.message
                }, 'error');
                debug(`Sales orders error: ${error.message}`);
            }
        }
        
        async function createDeliveryNote() {
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    log('deliveryNoteResult', {
                        status: 'NO_TOKEN',
                        message: 'Please login first'
                    }, 'error');
                    return;
                }
                
                const salesOrderId = parseInt(document.getElementById('salesOrderId').value);
                const customerId = parseInt(document.getElementById('customerId').value);
                const deliveryAddress = document.getElementById('deliveryAddress').value;
                
                const deliveryNoteData = {
                    sales_order: salesOrderId,
                    customer: customerId,
                    delivery_date: new Date().toISOString().split('T')[0],
                    expected_delivery_date: new Date(Date.now() + 24*60*60*1000).toISOString().split('T')[0],
                    delivery_address: deliveryAddress,
                    delivery_contact_person: '',
                    delivery_contact_phone: '',
                    vehicle_number: '',
                    driver_name: '',
                    driver_phone: '',
                    notes: '',
                    internal_notes: '',
                    status: 'draft'
                };
                
                debug('Creating delivery note with data:');
                debug(deliveryNoteData);
                
                const response = await fetch(`${API_BASE}/sales/delivery-notes/`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Token ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(deliveryNoteData)
                });
                
                debug(`Delivery note response status: ${response.status}`);
                debug(`Response headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()))}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log('deliveryNoteResult', {
                        status: 'SUCCESS',
                        deliveryNote: data
                    }, 'success');
                    debug('Delivery note created successfully');
                } else {
                    const errorData = await response.text();
                    log('deliveryNoteResult', {
                        status: 'FAILED',
                        statusCode: response.status,
                        error: errorData
                    }, 'error');
                    debug(`Delivery note failed: ${response.status} - ${errorData}`);
                    
                    // Try to parse as JSON for better error details
                    try {
                        const errorJson = JSON.parse(errorData);
                        debug('Parsed error details:');
                        debug(errorJson);
                    } catch (e) {
                        debug('Could not parse error as JSON');
                    }
                }
            } catch (error) {
                log('deliveryNoteResult', {
                    status: 'ERROR',
                    message: error.message
                }, 'error');
                debug(`Delivery note error: ${error.message}`);
            }
        }
        
        // Auto-check token on page load
        window.onload = function() {
            checkToken();
            debug('Page loaded, ready for testing');
        };
    </script>
</body>
</html>

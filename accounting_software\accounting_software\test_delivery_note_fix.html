<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Delivery Note Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1000px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
        input, select { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
        .step { margin: 10px 0; }
        .step-title { font-weight: bold; margin-bottom: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Delivery Note Fix Test</h1>
        <p>This page tests the complete delivery note creation workflow to identify and fix the 500 error.</p>
        
        <div class="section">
            <h2>Step 1: Network & Server Check</h2>
            <button onclick="checkServer()">Check Server Connection</button>
            <div id="serverResult"></div>
        </div>
        
        <div class="section">
            <h2>Step 2: Authentication Test</h2>
            <button onclick="testAuth()">Test Login & Token</button>
            <div id="authResult"></div>
        </div>
        
        <div class="section">
            <h2>Step 3: API Endpoints Test</h2>
            <button onclick="testEndpoints()">Test All Endpoints</button>
            <div id="endpointsResult"></div>
        </div>
        
        <div class="section">
            <h2>Step 4: Delivery Note Creation Test</h2>
            <div class="step">
                <div class="step-title">Test Data:</div>
                <label>Sales Order ID: <input type="number" id="testSalesOrderId" value="20"></label><br>
                <label>Customer ID: <input type="number" id="testCustomerId" value="87"></label><br>
                <label>Delivery Address: <input type="text" id="testDeliveryAddress" value="Test Address"></label><br>
            </div>
            <button onclick="testDeliveryNoteCreation()">Create Delivery Note</button>
            <div id="deliveryNoteResult"></div>
        </div>
        
        <div class="section">
            <h2>Debug Log</h2>
            <button onclick="clearLog()">Clear Log</button>
            <div id="debugLog"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api';
        let authToken = null;
        
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
            element.innerHTML = `<div class="${className}"><pre>${JSON.stringify(message, null, 2)}</pre></div>`;
        }
        
        function debug(message) {
            const debugElement = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            debugElement.innerHTML += `<div class="info" style="margin: 5px 0; padding: 5px; font-size: 12px;">[${timestamp}] ${JSON.stringify(message, null, 2)}</div>`;
        }
        
        function clearLog() {
            document.getElementById('debugLog').innerHTML = '';
        }
        
        async function checkServer() {
            debug('Checking server connection...');
            
            try {
                // Test basic connectivity
                const response = await fetch('http://localhost:8000/api/', {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                debug(`Server response: ${response.status} ${response.statusText}`);
                
                if (response.ok) {
                    const data = await response.text();
                    log('serverResult', {
                        status: 'SUCCESS',
                        message: 'Server is running and accessible',
                        responseStatus: response.status,
                        responseSize: data.length
                    }, 'success');
                } else {
                    log('serverResult', {
                        status: 'WARNING',
                        message: 'Server responded but with non-200 status',
                        responseStatus: response.status
                    }, 'warning');
                }
            } catch (error) {
                debug(`Server check error: ${error.message}`);
                log('serverResult', {
                    status: 'ERROR',
                    message: 'Cannot connect to server',
                    error: error.message
                }, 'error');
            }
        }
        
        async function testAuth() {
            debug('Testing authentication...');
            
            try {
                const response = await fetch('http://localhost:8000/api-token-auth/', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                debug(`Auth response: ${response.status} ${response.statusText}`);
                
                if (response.ok) {
                    const data = await response.json();
                    authToken = data.token;
                    localStorage.setItem('token', authToken);
                    
                    log('authResult', {
                        status: 'SUCCESS',
                        message: 'Authentication successful',
                        tokenLength: authToken.length,
                        tokenPreview: authToken.substring(0, 20) + '...'
                    }, 'success');
                    
                    debug('Token stored in localStorage');
                } else {
                    const errorData = await response.text();
                    log('authResult', {
                        status: 'ERROR',
                        message: 'Authentication failed',
                        responseStatus: response.status,
                        error: errorData
                    }, 'error');
                }
            } catch (error) {
                debug(`Auth error: ${error.message}`);
                log('authResult', {
                    status: 'ERROR',
                    message: 'Authentication request failed',
                    error: error.message
                }, 'error');
            }
        }
        
        async function testEndpoints() {
            const token = localStorage.getItem('token') || authToken;
            
            if (!token) {
                log('endpointsResult', {
                    status: 'ERROR',
                    message: 'No authentication token. Please login first.'
                }, 'error');
                return;
            }
            
            debug('Testing API endpoints...');
            
            const endpoints = [
                { name: 'Sales Orders', url: '/sales/sales-orders/', method: 'GET' },
                { name: 'Delivery Notes', url: '/sales/delivery-notes/', method: 'GET' },
                { name: 'Customers', url: '/contacts/customers/', method: 'GET' }
            ];
            
            const results = [];
            
            for (const endpoint of endpoints) {
                try {
                    debug(`Testing ${endpoint.name}...`);
                    
                    const response = await fetch(`${API_BASE}${endpoint.url}?page_size=1`, {
                        method: endpoint.method,
                        headers: {
                            'Authorization': `Token ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    const result = {
                        name: endpoint.name,
                        status: response.status,
                        success: response.ok
                    };
                    
                    if (response.ok) {
                        const data = await response.json();
                        result.count = data.count || data.length || 'N/A';
                    } else {
                        result.error = await response.text();
                    }
                    
                    results.push(result);
                    debug(`${endpoint.name}: ${response.status}`);
                    
                } catch (error) {
                    results.push({
                        name: endpoint.name,
                        status: 'ERROR',
                        success: false,
                        error: error.message
                    });
                    debug(`${endpoint.name} error: ${error.message}`);
                }
            }
            
            const allSuccess = results.every(r => r.success);
            log('endpointsResult', {
                status: allSuccess ? 'SUCCESS' : 'PARTIAL',
                message: allSuccess ? 'All endpoints working' : 'Some endpoints failed',
                results: results
            }, allSuccess ? 'success' : 'warning');
        }
        
        async function testDeliveryNoteCreation() {
            const token = localStorage.getItem('token') || authToken;
            
            if (!token) {
                log('deliveryNoteResult', {
                    status: 'ERROR',
                    message: 'No authentication token. Please login first.'
                }, 'error');
                return;
            }
            
            const salesOrderId = parseInt(document.getElementById('testSalesOrderId').value);
            const customerId = parseInt(document.getElementById('testCustomerId').value);
            const deliveryAddress = document.getElementById('testDeliveryAddress').value;
            
            const deliveryNoteData = {
                sales_order: salesOrderId,
                customer: customerId,
                delivery_date: new Date().toISOString().split('T')[0],
                expected_delivery_date: new Date(Date.now() + 24*60*60*1000).toISOString().split('T')[0],
                delivery_address: deliveryAddress,
                delivery_contact_person: '',
                delivery_contact_phone: '',
                vehicle_number: '',
                driver_name: '',
                driver_phone: '',
                notes: '',
                internal_notes: '',
                status: 'draft'
            };
            
            debug('Creating delivery note...');
            debug(`Data: ${JSON.stringify(deliveryNoteData, null, 2)}`);
            
            try {
                const response = await fetch(`${API_BASE}/sales/delivery-notes/`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Token ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(deliveryNoteData)
                });
                
                debug(`Response: ${response.status} ${response.statusText}`);
                debug(`Response headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()))}`);
                
                if (response.ok) {
                    const result = await response.json();
                    log('deliveryNoteResult', {
                        status: 'SUCCESS',
                        message: '🎉 DELIVERY NOTE CREATED SUCCESSFULLY! 🎉',
                        deliveryNote: {
                            id: result.id,
                            gdn_number: result.gdn_number,
                            sales_order: result.sales_order,
                            customer_name: result.customer_name,
                            delivery_date: result.delivery_date,
                            status: result.status
                        },
                        fullResponse: result
                    }, 'success');
                    debug('✅ SUCCESS! Delivery note created');
                } else {
                    const errorText = await response.text();
                    let errorData;
                    try {
                        errorData = JSON.parse(errorText);
                    } catch {
                        errorData = errorText;
                    }
                    
                    log('deliveryNoteResult', {
                        status: 'ERROR',
                        message: `❌ FAILED: ${response.status} ${response.statusText}`,
                        responseStatus: response.status,
                        error: errorData,
                        requestData: deliveryNoteData
                    }, 'error');
                    debug(`❌ FAILED: ${response.status} - ${errorText}`);
                }
                
            } catch (error) {
                debug(`❌ REQUEST ERROR: ${error.message}`);
                log('deliveryNoteResult', {
                    status: 'ERROR',
                    message: 'Request failed',
                    error: error.message,
                    requestData: deliveryNoteData
                }, 'error');
            }
        }
        
        // Auto-run basic checks on page load
        window.onload = async function() {
            debug('Page loaded, starting automatic checks...');
            await checkServer();
            await testAuth();
            debug('Automatic checks complete. Ready for manual testing.');
        };
    </script>
</body>
</html>

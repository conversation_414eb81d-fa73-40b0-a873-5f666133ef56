#!/usr/bin/env python3
"""
Complete test to simulate the exact frontend delivery note creation scenario
"""
import os
import sys
import django
import requests
import json
from datetime import date, timedelta

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

def test_complete_frontend_scenario():
    """Test the complete frontend scenario step by step"""
    print("=== COMPLETE FRONTEND SCENARIO TEST ===")
    
    # Step 1: Login and get token
    print("\n1. Testing Login...")
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    try:
        login_response = requests.post(
            'http://localhost:8000/api-token-auth/',
            json=login_data
        )
        
        print(f"Login Status: {login_response.status_code}")
        
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.text}")
            return
        
        token = login_response.json().get('token')
        print(f"✅ Login successful, token: {token[:20]}...")
        
    except Exception as e:
        print(f"❌ Login error: {e}")
        return
    
    # Step 2: Test token validity
    print("\n2. Testing Token Validity...")
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        # Test with a simple endpoint
        test_response = requests.get(
            'http://localhost:8000/api/sales/sales-orders/',
            headers=headers,
            params={'page_size': 1}
        )
        
        print(f"Token Test Status: {test_response.status_code}")
        
        if test_response.status_code != 200:
            print(f"❌ Token invalid: {test_response.text}")
            return
        
        print("✅ Token is valid")
        
    except Exception as e:
        print(f"❌ Token test error: {e}")
        return
    
    # Step 3: Get sales order data
    print("\n3. Getting Sales Order Data...")
    try:
        so_response = requests.get(
            'http://localhost:8000/api/sales/sales-orders/',
            headers=headers,
            params={'page_size': 1}
        )
        
        sales_orders = so_response.json().get('results', [])
        if not sales_orders:
            print("❌ No sales orders found")
            return
        
        so = sales_orders[0]
        print(f"✅ Using Sales Order: {so['id']} - {so.get('so_number', 'N/A')}")
        print(f"   Customer: {so.get('customer_id')} / {so.get('customer')}")
        
    except Exception as e:
        print(f"❌ Sales order error: {e}")
        return
    
    # Step 4: Create delivery note with exact frontend data
    print("\n4. Creating Delivery Note...")
    
    # This is the exact data structure that frontend sends
    delivery_note_data = {
        'sales_order': so['id'],
        'customer': so.get('customer_id') or so.get('customer'),
        'delivery_date': str(date.today()),
        'expected_delivery_date': str(date.today() + timedelta(days=1)),
        'delivery_address': '',
        'delivery_contact_person': '',
        'delivery_contact_phone': '',
        'vehicle_number': '',
        'driver_name': '',
        'driver_phone': '',
        'notes': '',
        'internal_notes': '',
        'status': 'draft'
    }
    
    print(f"Delivery Note Data: {json.dumps(delivery_note_data, indent=2)}")
    
    try:
        # Make the exact request that frontend makes
        create_response = requests.post(
            'http://localhost:8000/api/sales/delivery-notes/',
            headers=headers,
            json=delivery_note_data
        )
        
        print(f"Create Response Status: {create_response.status_code}")
        print(f"Create Response Headers: {dict(create_response.headers)}")
        
        if create_response.status_code == 201:
            result = create_response.json()
            print(f"✅ SUCCESS! Created delivery note: {result.get('gdn_number')} (ID: {result.get('id')})")
            print(f"Response: {json.dumps(result, indent=2)}")
        else:
            print(f"❌ FAILED! Status: {create_response.status_code}")
            print(f"Response: {create_response.text}")
            
            # Try to parse error details
            try:
                error_data = create_response.json()
                print(f"Error Details: {json.dumps(error_data, indent=2)}")
            except:
                print("Could not parse error response as JSON")
        
    except Exception as e:
        print(f"❌ Create delivery note error: {e}")
        import traceback
        traceback.print_exc()

def test_with_different_auth_methods():
    """Test different authentication methods"""
    print("\n=== TESTING DIFFERENT AUTH METHODS ===")
    
    # Method 1: Token in header (current method)
    print("\n1. Testing Token in Authorization header...")
    token = get_token()
    if token:
        headers = {
            'Authorization': f'Token {token}',
            'Content-Type': 'application/json'
        }
        test_auth_method(headers, "Token in Authorization header")
    
    # Method 2: Token in different format
    print("\n2. Testing Token in Bearer format...")
    if token:
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        test_auth_method(headers, "Bearer token")

def get_token():
    """Get authentication token"""
    try:
        response = requests.post(
            'http://localhost:8000/api-token-auth/',
            json={'username': 'admin', 'password': 'admin123'}
        )
        if response.status_code == 200:
            return response.json().get('token')
    except:
        pass
    return None

def test_auth_method(headers, method_name):
    """Test a specific auth method"""
    try:
        response = requests.get(
            'http://localhost:8000/api/sales/sales-orders/',
            headers=headers,
            params={'page_size': 1}
        )
        print(f"   {method_name}: {response.status_code}")
    except Exception as e:
        print(f"   {method_name}: Error - {e}")

if __name__ == '__main__':
    test_complete_frontend_scenario()
    test_with_different_auth_methods()

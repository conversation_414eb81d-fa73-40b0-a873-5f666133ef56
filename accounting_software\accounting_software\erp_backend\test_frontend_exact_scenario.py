#!/usr/bin/env python3
"""
Test script to simulate the exact frontend delivery note creation scenario
"""
import os
import sys
import django
import requests
import json
from datetime import date, timedelta

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

def get_auth_token():
    """Get authentication token"""
    try:
        # Try to login as admin
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = requests.post(
            'http://localhost:8000/api-token-auth/',
            json=login_data
        )
        
        if response.status_code == 200:
            token = response.json().get('token')
            print(f"✅ Got auth token: {token[:20]}...")
            return token
        else:
            print(f"❌ Login failed: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error getting token: {e}")
        return None

def test_frontend_exact_scenario():
    """Test the exact scenario that frontend is trying"""
    print("\n=== Testing Frontend Exact Scenario ===")
    
    # Get auth token
    token = get_auth_token()
    if not token:
        print("❌ Cannot proceed without auth token")
        return
    
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        # First, get a sales order to use
        so_response = requests.get(
            'http://localhost:8000/api/sales/sales-orders/',
            headers=headers,
            params={'page_size': 1}
        )
        
        if so_response.status_code != 200:
            print(f"❌ Failed to get sales orders: {so_response.status_code}")
            return
            
        sales_orders = so_response.json().get('results', [])
        if not sales_orders:
            print("❌ No sales orders found")
            return
            
        so = sales_orders[0]
        print(f"Using Sales Order: {so['id']} - {so.get('so_number', 'N/A')}")
        
        # Create delivery note with exact frontend data structure
        delivery_note_data = {
            'sales_order': so['id'],
            'customer': so.get('customer_id') or so.get('customer'),
            'delivery_date': str(date.today()),
            'expected_delivery_date': str(date.today() + timedelta(days=1)),
            'delivery_address': 'Test Address Frontend Exact',
            'delivery_contact_person': '',
            'delivery_contact_phone': '',
            'vehicle_number': '',
            'driver_name': '',
            'driver_phone': '',
            'notes': '',
            'internal_notes': '',
            'status': 'draft'
        }
        
        print(f"Frontend exact data: {json.dumps(delivery_note_data, indent=2)}")
        
        # Make the request exactly like frontend does
        response = requests.post(
            'http://localhost:8000/api/sales/delivery-notes/',
            headers=headers,
            json=delivery_note_data
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 201:
            print("✅ Frontend exact scenario test PASSED")
            result = response.json()
            print(f"Created delivery note: {result.get('gdn_number')} (ID: {result.get('id')})")
        else:
            print(f"❌ Frontend exact scenario FAILED: {response.status_code}")
            print(f"Response content: {response.text}")
            
            # Try to parse error details
            try:
                error_data = response.json()
                print(f"Error details: {json.dumps(error_data, indent=2)}")
            except:
                print("Could not parse error response as JSON")
        
    except Exception as e:
        print(f"❌ Error in frontend exact scenario: {e}")
        import traceback
        traceback.print_exc()

def test_with_minimal_data():
    """Test with minimal required data only"""
    print("\n=== Testing Minimal Data ===")
    
    token = get_auth_token()
    if not token:
        return
    
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        # Get a sales order
        so_response = requests.get(
            'http://localhost:8000/api/sales/sales-orders/',
            headers=headers,
            params={'page_size': 1}
        )
        
        sales_orders = so_response.json().get('results', [])
        if not sales_orders:
            print("❌ No sales orders found")
            return
            
        so = sales_orders[0]
        
        # Minimal data
        minimal_data = {
            'sales_order': so['id'],
            'delivery_date': str(date.today()),
            'status': 'draft'
        }
        
        print(f"Minimal data: {json.dumps(minimal_data, indent=2)}")
        
        response = requests.post(
            'http://localhost:8000/api/sales/delivery-notes/',
            headers=headers,
            json=minimal_data
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 201:
            print("✅ Minimal data test PASSED")
        else:
            print(f"❌ Minimal data test FAILED: {response.status_code}")
            print(f"Response: {response.text}")
        
    except Exception as e:
        print(f"❌ Error in minimal data test: {e}")

if __name__ == '__main__':
    test_frontend_exact_scenario()
    test_with_minimal_data()

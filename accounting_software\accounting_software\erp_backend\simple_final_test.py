import requests

auth_response = requests.post('http://localhost:8000/api-token-auth/', {'username': 'admin', 'password': 'admin123'})
token = auth_response.json()['token']
headers = {'Authorization': f'Token {token}'}

print("Testing key endpoints:")

# Test delivery notes (was causing 500 error)
response = requests.get('http://localhost:8000/api/sales/delivery-notes/', headers=headers)
print(f'1. Delivery notes: {response.status_code}')

# Test pricing products (should show 27 products)
response = requests.get('http://localhost:8000/api/pricing/product-costs/all/', headers=headers)
print(f'2. Pricing products: {response.status_code}')
if response.status_code == 200:
    data = response.json()
    print(f'   Products count: {len(data)}')

print("✅ Tests complete!")

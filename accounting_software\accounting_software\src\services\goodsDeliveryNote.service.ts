import api from './api';

export interface GoodsDeliveryNote {
  id?: number;
  gdn_id?: string;
  gdn_number?: string;
  sales_order: number;
  sales_order_number?: string;
  customer?: number;
  customer_name?: string;
  delivery_date: string;
  expected_delivery_date?: string;
  actual_delivery_date?: string;
  delivery_address?: string;
  delivery_contact_person?: string;
  delivery_contact_phone?: string;
  vehicle_number?: string;
  driver_name?: string;
  driver_phone?: string;
  status: 'draft' | 'confirmed' | 'delivered' | 'returned' | 'cancelled';
  notes?: string;
  internal_notes?: string;
  customer_signature?: string;
  received_by?: string;
  received_date?: string;
  line_items?: GoodsDeliveryNoteLineItem[];
  created_at?: string;
  updated_at?: string;
}

export interface GoodsDeliveryNoteLineItem {
  id?: number;
  sales_order_line_item: number;
  product?: number;
  product_name?: string;
  product_code?: string;
  description: string;
  quantity_ordered: number;
  quantity_delivered: number;
  quantity_remaining: number;
  unit_of_measure: string;
  unit_price: number;
  line_order: number;
  notes?: string;
}

export interface GoodsDeliveryReturnNote {
  id?: number;
  gdrn_id?: string;
  gdrn_number?: string;
  delivery_note: number;
  delivery_note_number?: string;
  customer?: number;
  customer_name?: string;
  return_date: string;
  expected_return_date?: string;
  actual_return_date?: string;
  return_reason: 'defective' | 'wrong_item' | 'damaged' | 'customer_request' | 'quality_issue' | 'other';
  return_address?: string;
  return_contact_person?: string;
  return_contact_phone?: string;
  status: 'draft' | 'confirmed' | 'received' | 'processed' | 'cancelled';
  notes?: string;
  internal_notes?: string;
  quality_check_passed: boolean;
  quality_check_notes?: string;
  quality_checked_by?: string;
  quality_check_date?: string;
  line_items?: GoodsDeliveryReturnNoteLineItem[];
  created_at?: string;
  updated_at?: string;
}

export interface GoodsDeliveryReturnNoteLineItem {
  id?: number;
  delivery_note_line_item: number;
  product?: number;
  product_name?: string;
  product_code?: string;
  description: string;
  quantity_delivered: number;
  quantity_returned: number;
  unit_of_measure: string;
  unit_price: number;
  return_reason?: string;
  condition: 'good' | 'damaged' | 'defective' | 'expired';
  line_order: number;
  notes?: string;
}

class GoodsDeliveryNoteService {
  private baseUrl = '/sales/delivery-notes';
  private returnNotesUrl = '/sales/return-notes';

  // Delivery Notes
  async getAll(params?: {
    page?: number;
    page_size?: number;
    search?: string;
    status?: string;
    customer?: number;
    sales_order?: number;
    start_date?: string;
    end_date?: string;
  }) {
    const response = await api.get(this.baseUrl, { params });
    return response.data;
  }

  async getById(id: number) {
    const response = await api.get(`${this.baseUrl}/${id}/`);
    return response.data;
  }

  async create(deliveryNote: Partial<GoodsDeliveryNote>) {
    console.log('Creating delivery note:', {
      url: this.baseUrl,
      data: deliveryNote,
      hasToken: !!localStorage.getItem('token')
    });

    try {
      const response = await api.post(this.baseUrl, deliveryNote);
      console.log('Delivery note created successfully:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Delivery note creation failed:', {
        error: error.message,
        response: error.response,
        status: error.response?.status,
        data: error.response?.data,
        config: error.config
      });
      throw error;
    }
  }

  async update(id: number, deliveryNote: Partial<GoodsDeliveryNote>) {
    const response = await api.put(`${this.baseUrl}/${id}/`, deliveryNote);
    return response.data;
  }

  async delete(id: number) {
    const response = await api.delete(`${this.baseUrl}/${id}/`);
    return response.data;
  }

  async confirmDelivery(id: number, data: {
    received_by?: string;
    customer_signature?: string;
  }) {
    const response = await api.post(`${this.baseUrl}/${id}/confirm_delivery/`, data);
    return response.data;
  }

  async createInvoice(id: number, data: {
    invoice_date?: string;
    due_date?: string;
    payment_terms?: string;
    notes?: string;
  }) {
    const response = await api.post(`${this.baseUrl}/${id}/create_invoice/`, data);
    return response.data;
  }

  // Return Notes
  async getAllReturnNotes(params?: {
    page?: number;
    page_size?: number;
    search?: string;
    status?: string;
    return_reason?: string;
    customer?: number;
    delivery_note?: number;
    start_date?: string;
    end_date?: string;
  }) {
    const response = await api.get(this.returnNotesUrl, { params });
    return response.data;
  }

  async getReturnNoteById(id: number) {
    const response = await api.get(`${this.returnNotesUrl}/${id}/`);
    return response.data;
  }

  async createReturnNote(returnNote: Partial<GoodsDeliveryReturnNote>) {
    const response = await api.post(this.returnNotesUrl, returnNote);
    return response.data;
  }

  async updateReturnNote(id: number, returnNote: Partial<GoodsDeliveryReturnNote>) {
    const response = await api.put(`${this.returnNotesUrl}/${id}/`, returnNote);
    return response.data;
  }

  async deleteReturnNote(id: number) {
    const response = await api.delete(`${this.returnNotesUrl}/${id}/`);
    return response.data;
  }

  async confirmReturn(id: number, data: {
    quality_check_passed?: boolean;
    quality_check_notes?: string;
    quality_checked_by?: string;
  }) {
    const response = await api.post(`${this.returnNotesUrl}/${id}/confirm_return/`, data);
    return response.data;
  }

  async createCreditNote(id: number, data: {
    credit_date?: string;
    due_date?: string;
    notes?: string;
  }) {
    const response = await api.post(`${this.returnNotesUrl}/${id}/create_credit_note/`, data);
    return response.data;
  }

  // Utility methods
  getStatusColor(status: string): string {
    const statusColors: { [key: string]: string } = {
      draft: '#9e9e9e',
      confirmed: '#ff9800',
      delivered: '#4caf50',
      returned: '#2196f3',
      cancelled: '#f44336',
      received: '#8bc34a',
      processed: '#607d8b',
    };
    return statusColors[status] || '#9e9e9e';
  }

  getStatusLabel(status: string): string {
    const statusLabels: { [key: string]: string } = {
      draft: 'Draft',
      confirmed: 'Confirmed',
      delivered: 'Delivered',
      returned: 'Returned',
      cancelled: 'Cancelled',
      received: 'Received',
      processed: 'Processed',
    };
    return statusLabels[status] || status;
  }

  getReturnReasonLabel(reason: string): string {
    const reasonLabels: { [key: string]: string } = {
      defective: 'Defective Product',
      wrong_item: 'Wrong Item Delivered',
      damaged: 'Damaged in Transit',
      customer_request: 'Customer Request',
      quality_issue: 'Quality Issue',
      other: 'Other',
    };
    return reasonLabels[reason] || reason;
  }

  getConditionLabel(condition: string): string {
    const conditionLabels: { [key: string]: string } = {
      good: 'Good Condition',
      damaged: 'Damaged',
      defective: 'Defective',
      expired: 'Expired',
    };
    return conditionLabels[condition] || condition;
  }

  getConditionColor(condition: string): string {
    const conditionColors: { [key: string]: string } = {
      good: '#4caf50',
      damaged: '#ff9800',
      defective: '#f44336',
      expired: '#9c27b0',
    };
    return conditionColors[condition] || '#9e9e9e';
  }
}

export const goodsDeliveryNoteService = new GoodsDeliveryNoteService();
export default goodsDeliveryNoteService;
